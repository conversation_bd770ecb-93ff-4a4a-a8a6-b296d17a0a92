[{"C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\authContext.tsx": "3", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\playerContext.tsx": "4", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\hostContext.tsx": "5", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\timeListenerContext.tsx": "6", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\soundContext.tsx": "7", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\routes\\ProtectedRoute.tsx": "8", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\HostFinalScore.tsx": "9", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\FallBack.tsx": "10", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Spectator\\SpectatorJoin.tsx": "11", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\PlayerFinalScore.tsx": "12", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\JoinRoom\\JoinRoom.tsx": "13", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Login\\Login.tsx": "14", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\UserRound1.tsx": "15", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\UserRound2.tsx": "16", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\RoundTurn\\UserRoundTurn.tsx": "17", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\InformationForm.tsx": "18", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round4\\UserRound4.tsx": "19", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Home\\Home.tsx": "20", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round3\\UserRound3.tsx": "21", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Room\\CreateRoom.tsx": "22", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound3.tsx": "23", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRoundTurn.tsx": "24", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound4.tsx": "25", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound2.tsx": "26", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\Dashboard.tsx": "27", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound1.tsx": "28", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\services.ts": "29", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\service.ts": "30", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\service.ts": "31", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\firebaseServices.ts": "32", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\services.ts": "33", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\auth.service.ts": "34", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\FinalScore.tsx": "35", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useAuth.ts": "36", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\uploadAssestServices.ts": "37", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\room.service.ts": "38", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Footer.tsx": "39", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round1.tsx": "40", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round2.tsx": "41", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round3.tsx": "42", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\UploadTest.tsx": "43", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\SetUpMatch.tsx": "44", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\ViewTest.tsx": "45", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Player\\PlayerQuestionBoxRoundTurn.tsx": "46", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\User\\User.tsx": "47", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Player\\PlayerQuestionBoxRound4.tsx": "48", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\History.tsx": "49", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Host\\Host.tsx": "50", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Host\\HostQuestionBoxRoundTurn.tsx": "51", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Host\\HostQuestionBoxRound4.tsx": "52", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\http.ts": "53", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\firebase-config.ts": "54", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerScore.tsx": "55", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Header.tsx": "56", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\services.ts": "57", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\testManagement.service.ts": "58", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\utils.ts": "59", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useListener.ts": "60", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\PlayerAnswerInput.tsx": "61", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\GameGrid.tsx": "62", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Play.tsx": "63", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerAnswer.tsx": "64", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostManagement.tsx": "65", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostAnswer.tsx": "66", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\utils\\processFile.utils.ts": "67", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostQuestionPreview.tsx": "68", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\RulesModal.tsx": "69", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostGuideModal.tsx": "70", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerColorSelector.tsx": "71", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\SimpleColorPicker.tsx": "72", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\tokenRefresh.service.ts": "73", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useTokenRefresh.ts": "74", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ErrorBoundary.tsx": "75", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\providers\\ReduxProvider.tsx": "76", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\index.ts": "77", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\authSlice.ts": "78", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\uiSlice.ts": "79", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\roomSlice.ts": "80", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\gameSlice.ts": "81", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round3.migrated.tsx": "82", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round1.migrated.tsx": "83", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round4.migrated.tsx": "84", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\RoundTurn\\UserRoundTurn.migrated.tsx": "85", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round2.migrated.tsx": "86", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\UserRound1.migrated.tsx": "87", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round4\\UserRound4.migrated.tsx": "88", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round3\\UserRound3.migrated.tsx": "89", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\UserRound2.migrated.tsx": "90", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\index.ts": "91", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\index.ts": "92", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\firebase\\index.ts": "93", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\index.ts": "94", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\api\\index.ts": "95", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Input\\index.ts": "96", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Button\\index.ts": "97", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\firebase\\useFirebaseListener.ts": "98", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\useLocalStorage.ts": "99", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\useDebounce.ts": "100", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\useAsync.ts": "101", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\api\\useAuthApi.ts": "102", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\api\\useGameApi.ts": "103", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Input\\Input.tsx": "104", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Button\\Button.tsx": "105", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Button\\Button.variants.ts": "106", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Input\\Input.variants.ts": "107", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\auth\\tokenService.ts": "108", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\game\\gameApi.ts": "109", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\firebase\\realtime.ts": "110", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\auth\\authApi.ts": "111", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\firebase\\config.ts": "112", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\api\\client.ts": "113", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\constants\\index.ts": "114", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\constants\\api.constants.ts": "115", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\constants\\game.constants.ts": "116", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\User\\User.migrated.tsx": "117", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Play.migrated.tsx": "118", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerScore.migrated.tsx": "119", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerAnswer.migrated.tsx": "120", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\utils\\migration.ts": "121", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\PlayerFinalScore.migrated.tsx": "122", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\InformationForm.migrated.tsx": "123", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\HostFinalScore.migrated.tsx": "124", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\FinalScore.migrated.tsx": "125", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\index.ts": "126", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\room\\roomApi.ts": "127"}, {"size": 531, "mtime": 1742041483045, "results": "128", "hashOfConfig": "129"}, {"size": 8643, "mtime": 1753705223096, "results": "130", "hashOfConfig": "129"}, {"size": 1945, "mtime": 1751515978853, "results": "131", "hashOfConfig": "129"}, {"size": 3318, "mtime": 1751205902719, "results": "132", "hashOfConfig": "129"}, {"size": 12318, "mtime": 1753352498688, "results": "133", "hashOfConfig": "129"}, {"size": 3805, "mtime": 1753634072473, "results": "134", "hashOfConfig": "129"}, {"size": 2210, "mtime": 1748877596389, "results": "135", "hashOfConfig": "129"}, {"size": 5699, "mtime": 1753498440821, "results": "136", "hashOfConfig": "129"}, {"size": 3063, "mtime": 1750646733694, "results": "137", "hashOfConfig": "129"}, {"size": 10853, "mtime": 1751813331809, "results": "138", "hashOfConfig": "129"}, {"size": 5916, "mtime": 1751468518454, "results": "139", "hashOfConfig": "129"}, {"size": 1091, "mtime": 1750606536117, "results": "140", "hashOfConfig": "129"}, {"size": 8831, "mtime": 1751469082283, "results": "141", "hashOfConfig": "129"}, {"size": 5973, "mtime": 1751467878917, "results": "142", "hashOfConfig": "129"}, {"size": 2428, "mtime": 1748535778291, "results": "143", "hashOfConfig": "129"}, {"size": 2985, "mtime": 1748882058132, "results": "144", "hashOfConfig": "129"}, {"size": 2519, "mtime": 1750907852110, "results": "145", "hashOfConfig": "129"}, {"size": 9153, "mtime": 1752762362793, "results": "146", "hashOfConfig": "129"}, {"size": 3560, "mtime": 1750858199098, "results": "147", "hashOfConfig": "129"}, {"size": 9231, "mtime": 1748535442800, "results": "148", "hashOfConfig": "129"}, {"size": 2434, "mtime": 1748537556126, "results": "149", "hashOfConfig": "129"}, {"size": 2950, "mtime": 1749916604844, "results": "150", "hashOfConfig": "129"}, {"size": 286, "mtime": 1744882291961, "results": "151", "hashOfConfig": "129"}, {"size": 500, "mtime": 1750907175563, "results": "152", "hashOfConfig": "129"}, {"size": 1202, "mtime": 1749697405299, "results": "153", "hashOfConfig": "129"}, {"size": 1350, "mtime": 1747936224953, "results": "154", "hashOfConfig": "129"}, {"size": 6142, "mtime": 1751771488939, "results": "155", "hashOfConfig": "129"}, {"size": 391, "mtime": 1748164720734, "results": "156", "hashOfConfig": "129"}, {"size": 16423, "mtime": 1753352498684, "results": "157", "hashOfConfig": "129"}, {"size": 12288, "mtime": 1753352498728, "results": "158", "hashOfConfig": "129"}, {"size": 4013, "mtime": 1753352498721, "results": "159", "hashOfConfig": "129"}, {"size": 7173, "mtime": 1753714799899, "results": "160", "hashOfConfig": "129"}, {"size": 9582, "mtime": 1753352498732, "results": "161", "hashOfConfig": "129"}, {"size": 1461, "mtime": 1751771152469, "results": "162", "hashOfConfig": "129"}, {"size": 2665, "mtime": 1750606460471, "results": "163", "hashOfConfig": "129"}, {"size": 3632, "mtime": 1753497839714, "results": "164", "hashOfConfig": "129"}, {"size": 1791, "mtime": 1751515978848, "results": "165", "hashOfConfig": "129"}, {"size": 1175, "mtime": 1753352498744, "results": "166", "hashOfConfig": "129"}, {"size": 1170, "mtime": 1741426442370, "results": "167", "hashOfConfig": "129"}, {"size": 8561, "mtime": 1753868476192, "results": "168", "hashOfConfig": "129"}, {"size": 35961, "mtime": 1751815841773, "results": "169", "hashOfConfig": "129"}, {"size": 21763, "mtime": 1751815785464, "results": "170", "hashOfConfig": "129"}, {"size": 4939, "mtime": 1750003466586, "results": "171", "hashOfConfig": "129"}, {"size": 27019, "mtime": 1753352498724, "results": "172", "hashOfConfig": "129"}, {"size": 18850, "mtime": 1753352498726, "results": "173", "hashOfConfig": "129"}, {"size": 8504, "mtime": 1753439385757, "results": "174", "hashOfConfig": "129"}, {"size": 575, "mtime": 1748534874974, "results": "175", "hashOfConfig": "129"}, {"size": 5356, "mtime": 1750860093881, "results": "176", "hashOfConfig": "129"}, {"size": 7200, "mtime": 1750743744045, "results": "177", "hashOfConfig": "129"}, {"size": 524, "mtime": 1748606359769, "results": "178", "hashOfConfig": "129"}, {"size": 8786, "mtime": 1751211350591, "results": "179", "hashOfConfig": "129"}, {"size": 19437, "mtime": 1751783887714, "results": "180", "hashOfConfig": "129"}, {"size": 3477, "mtime": 1753352498741, "results": "181", "hashOfConfig": "129"}, {"size": 787, "mtime": 1748599490140, "results": "182", "hashOfConfig": "129"}, {"size": 4432, "mtime": 1753756572148, "results": "183", "hashOfConfig": "129"}, {"size": 4866, "mtime": 1751714260898, "results": "184", "hashOfConfig": "129"}, {"size": 2464, "mtime": 1753352498716, "results": "185", "hashOfConfig": "129"}, {"size": 968, "mtime": 1750004006914, "results": "186", "hashOfConfig": "129"}, {"size": 11090, "mtime": 1749823808632, "results": "187", "hashOfConfig": "129"}, {"size": 7991, "mtime": 1750859938041, "results": "188", "hashOfConfig": "129"}, {"size": 2489, "mtime": 1753868155775, "results": "189", "hashOfConfig": "129"}, {"size": 6286, "mtime": 1749654059721, "results": "190", "hashOfConfig": "129"}, {"size": 16710, "mtime": 1753711967845, "results": "191", "hashOfConfig": "129"}, {"size": 8887, "mtime": 1753714147232, "results": "192", "hashOfConfig": "129"}, {"size": 16923, "mtime": 1753868771901, "results": "193", "hashOfConfig": "129"}, {"size": 20121, "mtime": 1753756713831, "results": "194", "hashOfConfig": "129"}, {"size": 326, "mtime": 1750003544449, "results": "195", "hashOfConfig": "129"}, {"size": 2678, "mtime": 1751210716770, "results": "196", "hashOfConfig": "129"}, {"size": 8734, "mtime": 1751366308588, "results": "197", "hashOfConfig": "129"}, {"size": 8700, "mtime": 1751716766283, "results": "198", "hashOfConfig": "129"}, {"size": 9219, "mtime": 1751446268973, "results": "199", "hashOfConfig": "129"}, {"size": 3672, "mtime": 1751446814342, "results": "200", "hashOfConfig": "129"}, {"size": 5456, "mtime": 1751471046304, "results": "201", "hashOfConfig": "129"}, {"size": 3507, "mtime": 1751470890921, "results": "202", "hashOfConfig": "129"}, {"size": 1444, "mtime": 1751812774732, "results": "203", "hashOfConfig": "129"}, {"size": 385, "mtime": 1753352498671, "results": "204", "hashOfConfig": "129"}, {"size": 1076, "mtime": 1753352498670, "results": "205", "hashOfConfig": "129"}, {"size": 6648, "mtime": 1753439138768, "results": "206", "hashOfConfig": "129"}, {"size": 6936, "mtime": 1753352498676, "results": "207", "hashOfConfig": "129"}, {"size": 9411, "mtime": 1753705949759, "results": "208", "hashOfConfig": "129"}, {"size": 13874, "mtime": 1753937794946, "results": "209", "hashOfConfig": "129"}, {"size": 17705, "mtime": 1753352498709, "results": "210", "hashOfConfig": "129"}, {"size": 8179, "mtime": 1753356235722, "results": "211", "hashOfConfig": "129"}, {"size": 15023, "mtime": 1753352498713, "results": "212", "hashOfConfig": "129"}, {"size": 4673, "mtime": 1753352498736, "results": "213", "hashOfConfig": "129"}, {"size": 12611, "mtime": 1753352498706, "results": "214", "hashOfConfig": "129"}, {"size": 4797, "mtime": 1753352498737, "results": "215", "hashOfConfig": "129"}, {"size": 6664, "mtime": 1753352498735, "results": "216", "hashOfConfig": "129"}, {"size": 4223, "mtime": 1753352498734, "results": "217", "hashOfConfig": "129"}, {"size": 6238, "mtime": 1753352498733, "results": "218", "hashOfConfig": "129"}, {"size": 105, "mtime": 1753352498767, "results": "219", "hashOfConfig": "129"}, {"size": 264, "mtime": 1753352498753, "results": "220", "hashOfConfig": "129"}, {"size": 202, "mtime": 1753352498766, "results": "221", "hashOfConfig": "129"}, {"size": 187, "mtime": 1753352498761, "results": "222", "hashOfConfig": "129"}, {"size": 258, "mtime": 1753712759970, "results": "223", "hashOfConfig": "129"}, {"size": 182, "mtime": 1753352498753, "results": "224", "hashOfConfig": "129"}, {"size": 191, "mtime": 1753352498748, "results": "225", "hashOfConfig": "129"}, {"size": 13690, "mtime": 1753933873631, "results": "226", "hashOfConfig": "129"}, {"size": 2311, "mtime": 1753352498765, "results": "227", "hashOfConfig": "129"}, {"size": 1190, "mtime": 1753352498764, "results": "228", "hashOfConfig": "129"}, {"size": 2450, "mtime": 1753352498762, "results": "229", "hashOfConfig": "129"}, {"size": 3856, "mtime": 1753498312101, "results": "230", "hashOfConfig": "129"}, {"size": 9873, "mtime": 1753863321393, "results": "231", "hashOfConfig": "129"}, {"size": 2429, "mtime": 1753352498750, "results": "232", "hashOfConfig": "129"}, {"size": 1579, "mtime": 1753352498746, "results": "233", "hashOfConfig": "129"}, {"size": 1818, "mtime": 1753352498748, "results": "234", "hashOfConfig": "129"}, {"size": 1366, "mtime": 1753352498752, "results": "235", "hashOfConfig": "129"}, {"size": 4479, "mtime": 1753352498771, "results": "236", "hashOfConfig": "129"}, {"size": 6775, "mtime": 1753937130361, "results": "237", "hashOfConfig": "129"}, {"size": 6413, "mtime": 1753352498774, "results": "238", "hashOfConfig": "129"}, {"size": 2334, "mtime": 1753501956146, "results": "239", "hashOfConfig": "129"}, {"size": 820, "mtime": 1753352498772, "results": "240", "hashOfConfig": "129"}, {"size": 4648, "mtime": 1753500527475, "results": "241", "hashOfConfig": "129"}, {"size": 106, "mtime": 1753352498756, "results": "242", "hashOfConfig": "129"}, {"size": 2276, "mtime": 1753936297389, "results": "243", "hashOfConfig": "129"}, {"size": 1709, "mtime": 1753352498756, "results": "244", "hashOfConfig": "129"}, {"size": 1391, "mtime": 1753352498714, "results": "245", "hashOfConfig": "129"}, {"size": 9205, "mtime": 1753352498697, "results": "246", "hashOfConfig": "129"}, {"size": 7684, "mtime": 1753352498683, "results": "247", "hashOfConfig": "129"}, {"size": 11795, "mtime": 1753352498681, "results": "248", "hashOfConfig": "129"}, {"size": 4862, "mtime": 1753352498786, "results": "249", "hashOfConfig": "129"}, {"size": 2980, "mtime": 1753352498718, "results": "250", "hashOfConfig": "129"}, {"size": 10704, "mtime": 1753352498729, "results": "251", "hashOfConfig": "129"}, {"size": 6033, "mtime": 1753352498717, "results": "252", "hashOfConfig": "129"}, {"size": 12280, "mtime": 1753352498676, "results": "253", "hashOfConfig": "129"}, {"size": 311, "mtime": 1753352498777, "results": "254", "hashOfConfig": "129"}, {"size": 3382, "mtime": 1753352498778, "results": "255", "hashOfConfig": "129"}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "xa5pjk", {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 44, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 38, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\authContext.tsx", ["637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\playerContext.tsx", ["648", "649", "650"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\hostContext.tsx", ["651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\timeListenerContext.tsx", ["663", "664", "665", "666", "667", "668"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\soundContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\routes\\ProtectedRoute.tsx", ["669", "670"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\HostFinalScore.tsx", ["671", "672", "673", "674"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\FallBack.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Spectator\\SpectatorJoin.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\PlayerFinalScore.tsx", ["675"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\JoinRoom\\JoinRoom.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Login\\Login.tsx", ["676"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\UserRound1.tsx", ["677", "678"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\UserRound2.tsx", ["679", "680", "681", "682", "683", "684", "685", "686", "687"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\RoundTurn\\UserRoundTurn.tsx", ["688", "689"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\InformationForm.tsx", ["690"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round4\\UserRound4.tsx", ["691", "692", "693", "694", "695"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Home\\Home.tsx", ["696"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round3\\UserRound3.tsx", ["697", "698", "699"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Room\\CreateRoom.tsx", ["700", "701", "702", "703"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound3.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRoundTurn.tsx", ["704", "705"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound4.tsx", ["706", "707"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound2.tsx", ["708"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\Dashboard.tsx", ["709"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound1.tsx", ["710"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\services.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\service.ts", ["711", "712"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\service.ts", ["713", "714"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\firebaseServices.ts", ["715", "716"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\services.ts", ["717", "718"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\auth.service.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\FinalScore.tsx", ["719", "720", "721", "722", "723"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useAuth.ts", ["724", "725", "726", "727", "728", "729", "730", "731", "732", "733"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\uploadAssestServices.ts", ["734"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\room.service.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round1.tsx", ["735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round2.tsx", ["759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round3.tsx", ["788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\UploadTest.tsx", ["817", "818"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\SetUpMatch.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\ViewTest.tsx", ["819", "820", "821", "822", "823", "824"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Player\\PlayerQuestionBoxRoundTurn.tsx", ["825", "826", "827", "828", "829", "830", "831", "832", "833", "834"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\User\\User.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Player\\PlayerQuestionBoxRound4.tsx", ["835", "836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852", "853", "854", "855"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\History.tsx", ["856", "857", "858", "859", "860", "861", "862", "863", "864", "865"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Host\\Host.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Host\\HostQuestionBoxRoundTurn.tsx", ["866", "867", "868", "869", "870", "871", "872", "873", "874", "875"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Host\\HostQuestionBoxRound4.tsx", ["876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\http.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\firebase-config.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerScore.tsx", ["902", "903"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\services.ts", ["904"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\testManagement.service.ts", ["905"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\utils.ts", ["906", "907", "908", "909", "910", "911", "912", "913", "914"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useListener.ts", ["915", "916", "917", "918", "919", "920", "921", "922", "923", "924"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\PlayerAnswerInput.tsx", ["925", "926", "927", "928", "929", "930"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\GameGrid.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Play.tsx", ["931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955", "956", "957", "958", "959", "960", "961", "962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerAnswer.tsx", ["975", "976", "977", "978", "979", "980", "981", "982"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostManagement.tsx", ["983", "984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostAnswer.tsx", ["1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\utils\\processFile.utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostQuestionPreview.tsx", ["1020", "1021", "1022", "1023"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\RulesModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostGuideModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerColorSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\SimpleColorPicker.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\tokenRefresh.service.ts", ["1024"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useTokenRefresh.ts", ["1025"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\providers\\ReduxProvider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\authSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\uiSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\roomSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\gameSlice.ts", ["1026", "1027"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round3.migrated.tsx", ["1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round1.migrated.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round4.migrated.tsx", ["1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\RoundTurn\\UserRoundTurn.migrated.tsx", ["1047", "1048"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round2.migrated.tsx", ["1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\UserRound1.migrated.tsx", ["1087", "1088"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round4\\UserRound4.migrated.tsx", ["1089", "1090"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round3\\UserRound3.migrated.tsx", ["1091", "1092", "1093"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\UserRound2.migrated.tsx", ["1094", "1095", "1096", "1097", "1098"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\firebase\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\api\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Input\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Button\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\firebase\\useFirebaseListener.ts", ["1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121", "1122"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\useLocalStorage.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\useDebounce.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\useAsync.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\api\\useAuthApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\api\\useGameApi.ts", ["1123", "1124", "1125", "1126", "1127", "1128", "1129", "1130", "1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Input\\Input.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Button\\Button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Button\\Button.variants.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Input\\Input.variants.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\auth\\tokenService.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\game\\gameApi.ts", ["1141", "1142"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\firebase\\realtime.ts", ["1143", "1144", "1145"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\auth\\authApi.ts", ["1146"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\firebase\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\api\\client.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\constants\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\constants\\api.constants.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\constants\\game.constants.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\User\\User.migrated.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Play.migrated.tsx", ["1147", "1148", "1149", "1150", "1151", "1152", "1153", "1154", "1155", "1156", "1157", "1158"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerScore.migrated.tsx", ["1159"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerAnswer.migrated.tsx", ["1160", "1161", "1162", "1163", "1164", "1165"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\utils\\migration.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\PlayerFinalScore.migrated.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\InformationForm.migrated.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\HostFinalScore.migrated.tsx", ["1166", "1167", "1168", "1169"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\FinalScore.migrated.tsx", ["1170", "1171", "1172", "1173"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\room\\roomApi.ts", ["1174", "1175", "1176"], [], {"ruleId": "1177", "severity": 1, "message": "1178", "line": 2, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1181", "line": 3, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 3, "endColumn": 15}, {"ruleId": "1177", "severity": 1, "message": "1182", "line": 5, "column": 3, "nodeType": "1179", "messageId": "1180", "endLine": 5, "endColumn": 10}, {"ruleId": "1177", "severity": 1, "message": "1183", "line": 6, "column": 3, "nodeType": "1179", "messageId": "1180", "endLine": 6, "endColumn": 29}, {"ruleId": "1177", "severity": 1, "message": "1184", "line": 8, "column": 3, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 10}, {"ruleId": "1177", "severity": 1, "message": "1185", "line": 9, "column": 3, "nodeType": "1179", "messageId": "1180", "endLine": 9, "endColumn": 21}, {"ruleId": "1177", "severity": 1, "message": "1186", "line": 17, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 17, "endColumn": 23}, {"ruleId": "1177", "severity": 1, "message": "1187", "line": 17, "column": 25, "nodeType": "1179", "messageId": "1180", "endLine": 17, "endColumn": 41}, {"ruleId": "1177", "severity": 1, "message": "1188", "line": 18, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 18, "endColumn": 14}, {"ruleId": "1177", "severity": 1, "message": "1189", "line": 18, "column": 16, "nodeType": "1179", "messageId": "1180", "endLine": 18, "endColumn": 23}, {"ruleId": "1177", "severity": 1, "message": "1190", "line": 21, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 21, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1178", "line": 2, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1191", "line": 17, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 17, "endColumn": 22}, {"ruleId": "1177", "severity": 1, "message": "1192", "line": 17, "column": 24, "nodeType": "1179", "messageId": "1180", "endLine": 17, "endColumn": 39}, {"ruleId": "1177", "severity": 1, "message": "1193", "line": 7, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 7, "endColumn": 27}, {"ruleId": "1177", "severity": 1, "message": "1194", "line": 8, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1195", "line": 8, "column": 27, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 51}, {"ruleId": "1177", "severity": 1, "message": "1196", "line": 10, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 10, "endColumn": 15}, {"ruleId": "1177", "severity": 1, "message": "1197", "line": 15, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 15, "endColumn": 35}, {"ruleId": "1177", "severity": 1, "message": "1198", "line": 20, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 20, "endColumn": 15}, {"ruleId": "1177", "severity": 1, "message": "1199", "line": 21, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 21, "endColumn": 15}, {"ruleId": "1177", "severity": 1, "message": "1200", "line": 27, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 27, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1201", "line": 45, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 45, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1202", "line": 45, "column": 20, "nodeType": "1179", "messageId": "1180", "endLine": 45, "endColumn": 31}, {"ruleId": "1177", "severity": 1, "message": "1203", "line": 56, "column": 24, "nodeType": "1179", "messageId": "1180", "endLine": 56, "endColumn": 39}, {"ruleId": "1204", "severity": 1, "message": "1205", "line": 72, "column": 6, "nodeType": "1206", "endLine": 72, "endColumn": 8, "suggestions": "1207"}, {"ruleId": "1177", "severity": 1, "message": "1196", "line": 3, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 3, "endColumn": 15}, {"ruleId": "1177", "severity": 1, "message": "1208", "line": 5, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 5, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1199", "line": 34, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 34, "endColumn": 15}, {"ruleId": "1177", "severity": 1, "message": "1209", "line": 37, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 37, "endColumn": 17}, {"ruleId": "1204", "severity": 1, "message": "1210", "line": 62, "column": 6, "nodeType": "1206", "endLine": 62, "endColumn": 16, "suggestions": "1211"}, {"ruleId": "1177", "severity": 1, "message": "1212", "line": 98, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 98, "endColumn": 23}, {"ruleId": "1177", "severity": 1, "message": "1213", "line": 3, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 3, "endColumn": 19}, {"ruleId": "1204", "severity": 1, "message": "1214", "line": 130, "column": 8, "nodeType": "1206", "endLine": 130, "endColumn": 60, "suggestions": "1215"}, {"ruleId": "1177", "severity": 1, "message": "1216", "line": 1, "column": 38, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 47}, {"ruleId": "1177", "severity": 1, "message": "1217", "line": 1, "column": 49, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 55}, {"ruleId": "1177", "severity": 1, "message": "1218", "line": 1, "column": 57, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 68}, {"ruleId": "1204", "severity": 1, "message": "1219", "line": 54, "column": 6, "nodeType": "1206", "endLine": 54, "endColumn": 8, "suggestions": "1220"}, {"ruleId": "1204", "severity": 1, "message": "1221", "line": 27, "column": 8, "nodeType": "1206", "endLine": 27, "endColumn": 10, "suggestions": "1222"}, {"ruleId": "1223", "severity": 1, "message": "1224", "line": 100, "column": 17, "nodeType": "1225", "endLine": 100, "endColumn": 101}, {"ruleId": "1177", "severity": 1, "message": "1226", "line": 20, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 20, "endColumn": 21}, {"ruleId": "1204", "severity": 1, "message": "1227", "line": 66, "column": 8, "nodeType": "1206", "endLine": 66, "endColumn": 16, "suggestions": "1228"}, {"ruleId": "1177", "severity": 1, "message": "1229", "line": 4, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 4, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1230", "line": 6, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 6, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1231", "line": 17, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 17, "endColumn": 22}, {"ruleId": "1177", "severity": 1, "message": "1232", "line": 17, "column": 24, "nodeType": "1179", "messageId": "1180", "endLine": 17, "endColumn": 39}, {"ruleId": "1177", "severity": 1, "message": "1233", "line": 18, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 18, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1234", "line": 18, "column": 21, "nodeType": "1179", "messageId": "1180", "endLine": 18, "endColumn": 33}, {"ruleId": "1177", "severity": 1, "message": "1235", "line": 21, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 21, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1226", "line": 28, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 28, "endColumn": 19}, {"ruleId": "1204", "severity": 1, "message": "1227", "line": 73, "column": 6, "nodeType": "1206", "endLine": 73, "endColumn": 14, "suggestions": "1236"}, {"ruleId": "1177", "severity": 1, "message": "1226", "line": 20, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 20, "endColumn": 21}, {"ruleId": "1204", "severity": 1, "message": "1227", "line": 66, "column": 8, "nodeType": "1206", "endLine": 66, "endColumn": 16, "suggestions": "1237"}, {"ruleId": "1177", "severity": 1, "message": "1238", "line": 3, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 3, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1239", "line": 1, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 14}, {"ruleId": "1177", "severity": 1, "message": "1240", "line": 8, "column": 7, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1241", "line": 36, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 36, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1226", "line": 38, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 38, "endColumn": 21}, {"ruleId": "1204", "severity": 1, "message": "1227", "line": 83, "column": 8, "nodeType": "1206", "endLine": 83, "endColumn": 16, "suggestions": "1242"}, {"ruleId": "1204", "severity": 1, "message": "1243", "line": 20, "column": 6, "nodeType": "1206", "endLine": 20, "endColumn": 8, "suggestions": "1244"}, {"ruleId": "1177", "severity": 1, "message": "1235", "line": 14, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 14, "endColumn": 20}, {"ruleId": "1177", "severity": 1, "message": "1226", "line": 22, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 22, "endColumn": 21}, {"ruleId": "1204", "severity": 1, "message": "1227", "line": 67, "column": 8, "nodeType": "1206", "endLine": 67, "endColumn": 16, "suggestions": "1245"}, {"ruleId": "1177", "severity": 1, "message": "1178", "line": 1, "column": 27, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 36}, {"ruleId": "1177", "severity": 1, "message": "1246", "line": 3, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 3, "endColumn": 14}, {"ruleId": "1177", "severity": 1, "message": "1247", "line": 4, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 4, "endColumn": 12}, {"ruleId": "1177", "severity": 1, "message": "1248", "line": 7, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 7, "endColumn": 14}, {"ruleId": "1177", "severity": 1, "message": "1249", "line": 1, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 14}, {"ruleId": "1177", "severity": 1, "message": "1250", "line": 4, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 4, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1239", "line": 1, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 14}, {"ruleId": "1177", "severity": 1, "message": "1251", "line": 7, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 7, "endColumn": 12}, {"ruleId": "1204", "severity": 1, "message": "1219", "line": 32, "column": 7, "nodeType": "1206", "endLine": 32, "endColumn": 9, "suggestions": "1252"}, {"ruleId": "1177", "severity": 1, "message": "1253", "line": 15, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 15, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1249", "line": 1, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 14}, {"ruleId": "1254", "severity": 1, "message": "1255", "line": 60, "column": 25, "nodeType": "1256", "messageId": "1257", "endLine": 60, "endColumn": 27}, {"ruleId": "1254", "severity": 1, "message": "1255", "line": 65, "column": 61, "nodeType": "1256", "messageId": "1257", "endLine": 65, "endColumn": 63}, {"ruleId": "1177", "severity": 1, "message": "1258", "line": 1, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1259", "line": 2, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 29}, {"ruleId": "1177", "severity": 1, "message": "1260", "line": 3, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 3, "endColumn": 16}, {"ruleId": "1177", "severity": 1, "message": "1261", "line": 3, "column": 18, "nodeType": "1179", "messageId": "1180", "endLine": 3, "endColumn": 23}, {"ruleId": "1254", "severity": 1, "message": "1255", "line": 89, "column": 25, "nodeType": "1256", "messageId": "1257", "endLine": 89, "endColumn": 27}, {"ruleId": "1254", "severity": 1, "message": "1255", "line": 94, "column": 61, "nodeType": "1256", "messageId": "1257", "endLine": 94, "endColumn": 63}, {"ruleId": "1177", "severity": 1, "message": "1262", "line": 1, "column": 17, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1178", "line": 1, "column": 27, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 36}, {"ruleId": "1177", "severity": 1, "message": "1217", "line": 1, "column": 49, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 55}, {"ruleId": "1177", "severity": 1, "message": "1218", "line": 1, "column": 57, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 68}, {"ruleId": "1177", "severity": 1, "message": "1263", "line": 4, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 4, "endColumn": 23}, {"ruleId": "1177", "severity": 1, "message": "1217", "line": 1, "column": 31, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 37}, {"ruleId": "1177", "severity": 1, "message": "1264", "line": 13, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 13, "endColumn": 21}, {"ruleId": "1177", "severity": 1, "message": "1265", "line": 13, "column": 23, "nodeType": "1179", "messageId": "1180", "endLine": 13, "endColumn": 34}, {"ruleId": "1177", "severity": 1, "message": "1266", "line": 15, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 15, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1267", "line": 15, "column": 21, "nodeType": "1179", "messageId": "1180", "endLine": 15, "endColumn": 29}, {"ruleId": "1177", "severity": 1, "message": "1268", "line": 15, "column": 31, "nodeType": "1179", "messageId": "1180", "endLine": 15, "endColumn": 42}, {"ruleId": "1177", "severity": 1, "message": "1213", "line": 16, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 16, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1269", "line": 17, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 17, "endColumn": 24}, {"ruleId": "1204", "severity": 1, "message": "1270", "line": 45, "column": 6, "nodeType": "1206", "endLine": 45, "endColumn": 8, "suggestions": "1271"}, {"ruleId": "1177", "severity": 1, "message": "1272", "line": 98, "column": 15, "nodeType": "1179", "messageId": "1180", "endLine": 98, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1273", "line": 33, "column": 16, "nodeType": "1179", "messageId": "1180", "endLine": 33, "endColumn": 41}, {"ruleId": "1177", "severity": 1, "message": "1251", "line": 2, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 12}, {"ruleId": "1177", "severity": 1, "message": "1274", "line": 3, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 3, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1275", "line": 9, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 9, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1276", "line": 10, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 10, "endColumn": 22}, {"ruleId": "1177", "severity": 1, "message": "1277", "line": 12, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 12, "endColumn": 13}, {"ruleId": "1177", "severity": 1, "message": "1199", "line": 31, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 31, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1278", "line": 34, "column": 29, "nodeType": "1179", "messageId": "1180", "endLine": 34, "endColumn": 47}, {"ruleId": "1177", "severity": 1, "message": "1279", "line": 35, "column": 27, "nodeType": "1179", "messageId": "1180", "endLine": 35, "endColumn": 43}, {"ruleId": "1177", "severity": 1, "message": "1280", "line": 36, "column": 24, "nodeType": "1179", "messageId": "1180", "endLine": 36, "endColumn": 37}, {"ruleId": "1177", "severity": 1, "message": "1201", "line": 38, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 38, "endColumn": 21}, {"ruleId": "1177", "severity": 1, "message": "1281", "line": 38, "column": 23, "nodeType": "1179", "messageId": "1180", "endLine": 38, "endColumn": 39}, {"ruleId": "1177", "severity": 1, "message": "1202", "line": 38, "column": 53, "nodeType": "1179", "messageId": "1180", "endLine": 38, "endColumn": 64}, {"ruleId": "1177", "severity": 1, "message": "1282", "line": 39, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 39, "endColumn": 26}, {"ruleId": "1177", "severity": 1, "message": "1283", "line": 39, "column": 45, "nodeType": "1179", "messageId": "1180", "endLine": 39, "endColumn": 53}, {"ruleId": "1177", "severity": 1, "message": "1284", "line": 39, "column": 55, "nodeType": "1179", "messageId": "1180", "endLine": 39, "endColumn": 67}, {"ruleId": "1177", "severity": 1, "message": "1203", "line": 39, "column": 69, "nodeType": "1179", "messageId": "1180", "endLine": 39, "endColumn": 84}, {"ruleId": "1177", "severity": 1, "message": "1285", "line": 39, "column": 86, "nodeType": "1179", "messageId": "1180", "endLine": 39, "endColumn": 103}, {"ruleId": "1177", "severity": 1, "message": "1286", "line": 39, "column": 105, "nodeType": "1179", "messageId": "1180", "endLine": 39, "endColumn": 124}, {"ruleId": "1177", "severity": 1, "message": "1287", "line": 40, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 40, "endColumn": 26}, {"ruleId": "1177", "severity": 1, "message": "1288", "line": 41, "column": 47, "nodeType": "1179", "messageId": "1180", "endLine": 41, "endColumn": 70}, {"ruleId": "1204", "severity": 1, "message": "1289", "line": 45, "column": 8, "nodeType": "1206", "endLine": 45, "endColumn": 33, "suggestions": "1290"}, {"ruleId": "1177", "severity": 1, "message": "1212", "line": 47, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 47, "endColumn": 25}, {"ruleId": "1204", "severity": 1, "message": "1291", "line": 56, "column": 8, "nodeType": "1206", "endLine": 56, "endColumn": 10, "suggestions": "1292"}, {"ruleId": "1204", "severity": 1, "message": "1293", "line": 68, "column": 8, "nodeType": "1206", "endLine": 68, "endColumn": 10, "suggestions": "1294"}, {"ruleId": "1177", "severity": 1, "message": "1251", "line": 1, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 12}, {"ruleId": "1177", "severity": 1, "message": "1295", "line": 3, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 3, "endColumn": 20}, {"ruleId": "1177", "severity": 1, "message": "1296", "line": 18, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 18, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1297", "line": 54, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 54, "endColumn": 27}, {"ruleId": "1177", "severity": 1, "message": "1298", "line": 60, "column": 7, "nodeType": "1179", "messageId": "1180", "endLine": 60, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1198", "line": 72, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 72, "endColumn": 11}, {"ruleId": "1177", "severity": 1, "message": "1202", "line": 74, "column": 33, "nodeType": "1179", "messageId": "1180", "endLine": 74, "endColumn": 44}, {"ruleId": "1177", "severity": 1, "message": "1284", "line": 77, "column": 27, "nodeType": "1179", "messageId": "1180", "endLine": 77, "endColumn": 39}, {"ruleId": "1177", "severity": 1, "message": "1299", "line": 101, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 101, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1300", "line": 101, "column": 27, "nodeType": "1179", "messageId": "1180", "endLine": 101, "endColumn": 45}, {"ruleId": "1177", "severity": 1, "message": "1301", "line": 102, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 102, "endColumn": 26}, {"ruleId": "1177", "severity": 1, "message": "1302", "line": 103, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 103, "endColumn": 31}, {"ruleId": "1204", "severity": 1, "message": "1303", "line": 122, "column": 6, "nodeType": "1206", "endLine": 122, "endColumn": 16, "suggestions": "1304"}, {"ruleId": "1204", "severity": 1, "message": "1305", "line": 142, "column": 6, "nodeType": "1206", "endLine": 142, "endColumn": 8, "suggestions": "1306"}, {"ruleId": "1177", "severity": 1, "message": "1307", "line": 147, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 147, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1308", "line": 148, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 148, "endColumn": 22}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 154, "column": 34, "nodeType": "1311", "messageId": "1312", "endLine": 154, "endColumn": 36}, {"ruleId": "1177", "severity": 1, "message": "1313", "line": 206, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 206, "endColumn": 19}, {"ruleId": "1204", "severity": 1, "message": "1314", "line": 232, "column": 6, "nodeType": "1206", "endLine": 232, "endColumn": 14, "suggestions": "1315"}, {"ruleId": "1204", "severity": 1, "message": "1221", "line": 251, "column": 6, "nodeType": "1206", "endLine": 251, "endColumn": 8, "suggestions": "1316"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 293, "column": 38, "nodeType": "1311", "messageId": "1312", "endLine": 293, "endColumn": 40}, {"ruleId": "1204", "severity": 1, "message": "1317", "line": 326, "column": 6, "nodeType": "1206", "endLine": 326, "endColumn": 48, "suggestions": "1318"}, {"ruleId": "1254", "severity": 1, "message": "1255", "line": 376, "column": 19, "nodeType": "1256", "messageId": "1257", "endLine": 376, "endColumn": 21}, {"ruleId": "1254", "severity": 1, "message": "1255", "line": 436, "column": 19, "nodeType": "1256", "messageId": "1257", "endLine": 436, "endColumn": 21}, {"ruleId": "1204", "severity": 1, "message": "1319", "line": 633, "column": 6, "nodeType": "1206", "endLine": 633, "endColumn": 20, "suggestions": "1320"}, {"ruleId": "1204", "severity": 1, "message": "1321", "line": 647, "column": 6, "nodeType": "1206", "endLine": 647, "endColumn": 8, "suggestions": "1322"}, {"ruleId": "1204", "severity": 1, "message": "1323", "line": 683, "column": 6, "nodeType": "1206", "endLine": 683, "endColumn": 20, "suggestions": "1324"}, {"ruleId": "1204", "severity": 1, "message": "1325", "line": 727, "column": 6, "nodeType": "1206", "endLine": 727, "endColumn": 20, "suggestions": "1326"}, {"ruleId": "1204", "severity": 1, "message": "1325", "line": 780, "column": 6, "nodeType": "1206", "endLine": 780, "endColumn": 20, "suggestions": "1327"}, {"ruleId": "1177", "severity": 1, "message": "1251", "line": 2, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 12}, {"ruleId": "1177", "severity": 1, "message": "1274", "line": 3, "column": 20, "nodeType": "1179", "messageId": "1180", "endLine": 3, "endColumn": 29}, {"ruleId": "1177", "severity": 1, "message": "1328", "line": 6, "column": 97, "nodeType": "1179", "messageId": "1180", "endLine": 6, "endColumn": 127}, {"ruleId": "1177", "severity": 1, "message": "1195", "line": 9, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 9, "endColumn": 34}, {"ruleId": "1177", "severity": 1, "message": "1329", "line": 12, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 12, "endColumn": 27}, {"ruleId": "1177", "severity": 1, "message": "1330", "line": 31, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 31, "endColumn": 30}, {"ruleId": "1177", "severity": 1, "message": "1331", "line": 36, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 36, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1284", "line": 40, "column": 83, "nodeType": "1179", "messageId": "1180", "endLine": 40, "endColumn": 95}, {"ruleId": "1177", "severity": 1, "message": "1332", "line": 41, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 41, "endColumn": 33}, {"ruleId": "1177", "severity": 1, "message": "1333", "line": 41, "column": 129, "nodeType": "1179", "messageId": "1180", "endLine": 41, "endColumn": 148}, {"ruleId": "1177", "severity": 1, "message": "1334", "line": 42, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 42, "endColumn": 38}, {"ruleId": "1177", "severity": 1, "message": "1335", "line": 43, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 43, "endColumn": 30}, {"ruleId": "1177", "severity": 1, "message": "1336", "line": 44, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 44, "endColumn": 28}, {"ruleId": "1177", "severity": 1, "message": "1337", "line": 44, "column": 30, "nodeType": "1179", "messageId": "1180", "endLine": 44, "endColumn": 49}, {"ruleId": "1177", "severity": 1, "message": "1338", "line": 48, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 48, "endColumn": 16}, {"ruleId": "1177", "severity": 1, "message": "1339", "line": 49, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 49, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1202", "line": 50, "column": 35, "nodeType": "1179", "messageId": "1180", "endLine": 50, "endColumn": 46}, {"ruleId": "1177", "severity": 1, "message": "1340", "line": 52, "column": 14, "nodeType": "1179", "messageId": "1180", "endLine": 52, "endColumn": 28}, {"ruleId": "1204", "severity": 1, "message": "1221", "line": 118, "column": 8, "nodeType": "1206", "endLine": 118, "endColumn": 10, "suggestions": "1341"}, {"ruleId": "1177", "severity": 1, "message": "1342", "line": 126, "column": 19, "nodeType": "1179", "messageId": "1180", "endLine": 126, "endColumn": 26}, {"ruleId": "1204", "severity": 1, "message": "1214", "line": 137, "column": 8, "nodeType": "1206", "endLine": 137, "endColumn": 10, "suggestions": "1343"}, {"ruleId": "1204", "severity": 1, "message": "1344", "line": 174, "column": 8, "nodeType": "1206", "endLine": 174, "endColumn": 24, "suggestions": "1345"}, {"ruleId": "1204", "severity": 1, "message": "1305", "line": 207, "column": 8, "nodeType": "1206", "endLine": 207, "endColumn": 10, "suggestions": "1346"}, {"ruleId": "1204", "severity": 1, "message": "1347", "line": 236, "column": 8, "nodeType": "1206", "endLine": 236, "endColumn": 18, "suggestions": "1348"}, {"ruleId": "1204", "severity": 1, "message": "1349", "line": 246, "column": 8, "nodeType": "1206", "endLine": 246, "endColumn": 10, "suggestions": "1350"}, {"ruleId": "1204", "severity": 1, "message": "1214", "line": 259, "column": 8, "nodeType": "1206", "endLine": 259, "endColumn": 10, "suggestions": "1351"}, {"ruleId": "1204", "severity": 1, "message": "1352", "line": 279, "column": 8, "nodeType": "1206", "endLine": 279, "endColumn": 10, "suggestions": "1353"}, {"ruleId": "1177", "severity": 1, "message": "1342", "line": 293, "column": 19, "nodeType": "1179", "messageId": "1180", "endLine": 293, "endColumn": 26}, {"ruleId": "1204", "severity": 1, "message": "1354", "line": 316, "column": 8, "nodeType": "1206", "endLine": 316, "endColumn": 10, "suggestions": "1355"}, {"ruleId": "1177", "severity": 1, "message": "1356", "line": 2, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 28}, {"ruleId": "1177", "severity": 1, "message": "1357", "line": 18, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 18, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1358", "line": 2, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1359", "line": 2, "column": 19, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 34}, {"ruleId": "1177", "severity": 1, "message": "1360", "line": 2, "column": 52, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 66}, {"ruleId": "1177", "severity": 1, "message": "1361", "line": 5, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 5, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1362", "line": 149, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 149, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1363", "line": 160, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 160, "endColumn": 26}, {"ruleId": "1177", "severity": 1, "message": "1277", "line": 12, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 12, "endColumn": 13}, {"ruleId": "1177", "severity": 1, "message": "1280", "line": 31, "column": 24, "nodeType": "1179", "messageId": "1180", "endLine": 31, "endColumn": 37}, {"ruleId": "1177", "severity": 1, "message": "1202", "line": 33, "column": 53, "nodeType": "1179", "messageId": "1180", "endLine": 33, "endColumn": 64}, {"ruleId": "1177", "severity": 1, "message": "1284", "line": 34, "column": 55, "nodeType": "1179", "messageId": "1180", "endLine": 34, "endColumn": 67}, {"ruleId": "1204", "severity": 1, "message": "1289", "line": 38, "column": 8, "nodeType": "1206", "endLine": 38, "endColumn": 33, "suggestions": "1364"}, {"ruleId": "1204", "severity": 1, "message": "1305", "line": 68, "column": 8, "nodeType": "1206", "endLine": 68, "endColumn": 10, "suggestions": "1365"}, {"ruleId": "1204", "severity": 1, "message": "1303", "line": 93, "column": 8, "nodeType": "1206", "endLine": 93, "endColumn": 18, "suggestions": "1366"}, {"ruleId": "1204", "severity": 1, "message": "1367", "line": 116, "column": 8, "nodeType": "1206", "endLine": 116, "endColumn": 10, "suggestions": "1368"}, {"ruleId": "1204", "severity": 1, "message": "1221", "line": 133, "column": 8, "nodeType": "1206", "endLine": 133, "endColumn": 10, "suggestions": "1369"}, {"ruleId": "1204", "severity": 1, "message": "1221", "line": 150, "column": 8, "nodeType": "1206", "endLine": 150, "endColumn": 10, "suggestions": "1370"}, {"ruleId": "1177", "severity": 1, "message": "1371", "line": 4, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 4, "endColumn": 20}, {"ruleId": "1177", "severity": 1, "message": "1372", "line": 4, "column": 41, "nodeType": "1179", "messageId": "1180", "endLine": 4, "endColumn": 54}, {"ruleId": "1177", "severity": 1, "message": "1373", "line": 4, "column": 56, "nodeType": "1179", "messageId": "1180", "endLine": 4, "endColumn": 73}, {"ruleId": "1177", "severity": 1, "message": "1374", "line": 4, "column": 75, "nodeType": "1179", "messageId": "1180", "endLine": 4, "endColumn": 95}, {"ruleId": "1177", "severity": 1, "message": "1375", "line": 4, "column": 97, "nodeType": "1179", "messageId": "1180", "endLine": 4, "endColumn": 114}, {"ruleId": "1177", "severity": 1, "message": "1376", "line": 4, "column": 116, "nodeType": "1179", "messageId": "1180", "endLine": 4, "endColumn": 131}, {"ruleId": "1177", "severity": 1, "message": "1230", "line": 4, "column": 133, "nodeType": "1179", "messageId": "1180", "endLine": 4, "endColumn": 148}, {"ruleId": "1177", "severity": 1, "message": "1377", "line": 4, "column": 150, "nodeType": "1179", "messageId": "1180", "endLine": 4, "endColumn": 162}, {"ruleId": "1177", "severity": 1, "message": "1378", "line": 20, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 20, "endColumn": 43}, {"ruleId": "1177", "severity": 1, "message": "1202", "line": 28, "column": 35, "nodeType": "1179", "messageId": "1180", "endLine": 28, "endColumn": 46}, {"ruleId": "1177", "severity": 1, "message": "1379", "line": 29, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 29, "endColumn": 28}, {"ruleId": "1177", "severity": 1, "message": "1380", "line": 29, "column": 30, "nodeType": "1179", "messageId": "1180", "endLine": 29, "endColumn": 49}, {"ruleId": "1177", "severity": 1, "message": "1381", "line": 35, "column": 18, "nodeType": "1179", "messageId": "1180", "endLine": 35, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1382", "line": 40, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 40, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1383", "line": 44, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 44, "endColumn": 34}, {"ruleId": "1177", "severity": 1, "message": "1384", "line": 44, "column": 36, "nodeType": "1179", "messageId": "1180", "endLine": 44, "endColumn": 59}, {"ruleId": "1177", "severity": 1, "message": "1385", "line": 44, "column": 61, "nodeType": "1179", "messageId": "1180", "endLine": 44, "endColumn": 82}, {"ruleId": "1177", "severity": 1, "message": "1386", "line": 44, "column": 84, "nodeType": "1179", "messageId": "1180", "endLine": 44, "endColumn": 92}, {"ruleId": "1177", "severity": 1, "message": "1284", "line": 44, "column": 94, "nodeType": "1179", "messageId": "1180", "endLine": 44, "endColumn": 106}, {"ruleId": "1204", "severity": 1, "message": "1347", "line": 83, "column": 8, "nodeType": "1206", "endLine": 83, "endColumn": 18, "suggestions": "1387"}, {"ruleId": "1204", "severity": 1, "message": "1305", "line": 102, "column": 8, "nodeType": "1206", "endLine": 102, "endColumn": 10, "suggestions": "1388"}, {"ruleId": "1177", "severity": 1, "message": "1217", "line": 1, "column": 38, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 44}, {"ruleId": "1177", "severity": 1, "message": "1358", "line": 2, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1359", "line": 2, "column": 19, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 34}, {"ruleId": "1177", "severity": 1, "message": "1389", "line": 2, "column": 36, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 50}, {"ruleId": "1177", "severity": 1, "message": "1360", "line": 2, "column": 52, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 66}, {"ruleId": "1177", "severity": 1, "message": "1390", "line": 3, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 3, "endColumn": 20}, {"ruleId": "1177", "severity": 1, "message": "1258", "line": 4, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 4, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1361", "line": 5, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 5, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1391", "line": 6, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 6, "endColumn": 29}, {"ruleId": "1177", "severity": 1, "message": "1392", "line": 27, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 27, "endColumn": 30}, {"ruleId": "1177", "severity": 1, "message": "1277", "line": 12, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 12, "endColumn": 13}, {"ruleId": "1177", "severity": 1, "message": "1280", "line": 31, "column": 24, "nodeType": "1179", "messageId": "1180", "endLine": 31, "endColumn": 37}, {"ruleId": "1177", "severity": 1, "message": "1202", "line": 33, "column": 53, "nodeType": "1179", "messageId": "1180", "endLine": 33, "endColumn": 64}, {"ruleId": "1177", "severity": 1, "message": "1284", "line": 34, "column": 55, "nodeType": "1179", "messageId": "1180", "endLine": 34, "endColumn": 67}, {"ruleId": "1204", "severity": 1, "message": "1289", "line": 38, "column": 8, "nodeType": "1206", "endLine": 38, "endColumn": 33, "suggestions": "1393"}, {"ruleId": "1204", "severity": 1, "message": "1305", "line": 68, "column": 8, "nodeType": "1206", "endLine": 68, "endColumn": 10, "suggestions": "1394"}, {"ruleId": "1204", "severity": 1, "message": "1303", "line": 93, "column": 8, "nodeType": "1206", "endLine": 93, "endColumn": 18, "suggestions": "1395"}, {"ruleId": "1204", "severity": 1, "message": "1367", "line": 116, "column": 8, "nodeType": "1206", "endLine": 116, "endColumn": 10, "suggestions": "1396"}, {"ruleId": "1204", "severity": 1, "message": "1221", "line": 133, "column": 8, "nodeType": "1206", "endLine": 133, "endColumn": 10, "suggestions": "1397"}, {"ruleId": "1204", "severity": 1, "message": "1221", "line": 150, "column": 8, "nodeType": "1206", "endLine": 150, "endColumn": 10, "suggestions": "1398"}, {"ruleId": "1177", "severity": 1, "message": "1251", "line": 2, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 12}, {"ruleId": "1177", "severity": 1, "message": "1274", "line": 3, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 3, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1371", "line": 8, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 20}, {"ruleId": "1177", "severity": 1, "message": "1399", "line": 8, "column": 22, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 39}, {"ruleId": "1177", "severity": 1, "message": "1372", "line": 8, "column": 41, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 54}, {"ruleId": "1177", "severity": 1, "message": "1373", "line": 8, "column": 56, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 73}, {"ruleId": "1177", "severity": 1, "message": "1374", "line": 8, "column": 75, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 95}, {"ruleId": "1177", "severity": 1, "message": "1375", "line": 8, "column": 97, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 114}, {"ruleId": "1177", "severity": 1, "message": "1376", "line": 8, "column": 116, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 131}, {"ruleId": "1177", "severity": 1, "message": "1230", "line": 8, "column": 133, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 148}, {"ruleId": "1177", "severity": 1, "message": "1377", "line": 8, "column": 150, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 162}, {"ruleId": "1177", "severity": 1, "message": "1400", "line": 20, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 20, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1240", "line": 31, "column": 7, "nodeType": "1179", "messageId": "1180", "endLine": 31, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1401", "line": 40, "column": 7, "nodeType": "1179", "messageId": "1180", "endLine": 40, "endColumn": 23}, {"ruleId": "1177", "severity": 1, "message": "1202", "line": 230, "column": 35, "nodeType": "1179", "messageId": "1180", "endLine": 230, "endColumn": 46}, {"ruleId": "1177", "severity": 1, "message": "1379", "line": 231, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 231, "endColumn": 28}, {"ruleId": "1177", "severity": 1, "message": "1383", "line": 246, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 246, "endColumn": 34}, {"ruleId": "1177", "severity": 1, "message": "1384", "line": 246, "column": 36, "nodeType": "1179", "messageId": "1180", "endLine": 246, "endColumn": 59}, {"ruleId": "1177", "severity": 1, "message": "1385", "line": 246, "column": 61, "nodeType": "1179", "messageId": "1180", "endLine": 246, "endColumn": 82}, {"ruleId": "1177", "severity": 1, "message": "1284", "line": 246, "column": 94, "nodeType": "1179", "messageId": "1180", "endLine": 246, "endColumn": 106}, {"ruleId": "1204", "severity": 1, "message": "1402", "line": 274, "column": 8, "nodeType": "1206", "endLine": 274, "endColumn": 10, "suggestions": "1403"}, {"ruleId": "1204", "severity": 1, "message": "1347", "line": 294, "column": 8, "nodeType": "1206", "endLine": 294, "endColumn": 18, "suggestions": "1404"}, {"ruleId": "1254", "severity": 1, "message": "1255", "line": 347, "column": 39, "nodeType": "1256", "messageId": "1257", "endLine": 347, "endColumn": 41}, {"ruleId": "1254", "severity": 1, "message": "1255", "line": 351, "column": 39, "nodeType": "1256", "messageId": "1257", "endLine": 351, "endColumn": 41}, {"ruleId": "1254", "severity": 1, "message": "1255", "line": 355, "column": 39, "nodeType": "1256", "messageId": "1257", "endLine": 355, "endColumn": 41}, {"ruleId": "1177", "severity": 1, "message": "1405", "line": 409, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 409, "endColumn": 30}, {"ruleId": "1177", "severity": 1, "message": "1406", "line": 13, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 13, "endColumn": 20}, {"ruleId": "1204", "severity": 1, "message": "1407", "line": 25, "column": 8, "nodeType": "1206", "endLine": 25, "endColumn": 55, "suggestions": "1408"}, {"ruleId": "1177", "severity": 1, "message": "1259", "line": 1, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 29}, {"ruleId": "1177", "severity": 1, "message": "1409", "line": 25, "column": 15, "nodeType": "1179", "messageId": "1180", "endLine": 25, "endColumn": 27}, {"ruleId": "1177", "severity": 1, "message": "1258", "line": 10, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 10, "endColumn": 21}, {"ruleId": "1177", "severity": 1, "message": "1410", "line": 69, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 69, "endColumn": 21}, {"ruleId": "1177", "severity": 1, "message": "1411", "line": 131, "column": 45, "nodeType": "1179", "messageId": "1180", "endLine": 131, "endColumn": 53}, {"ruleId": "1254", "severity": 1, "message": "1255", "line": 150, "column": 17, "nodeType": "1256", "messageId": "1257", "endLine": 150, "endColumn": 19}, {"ruleId": "1254", "severity": 1, "message": "1255", "line": 155, "column": 21, "nodeType": "1256", "messageId": "1257", "endLine": 155, "endColumn": 23}, {"ruleId": "1412", "severity": 1, "message": "1413", "line": 259, "column": 11, "nodeType": "1179", "messageId": "1414", "endLine": 259, "endColumn": 17}, {"ruleId": "1412", "severity": 1, "message": "1415", "line": 260, "column": 9, "nodeType": "1179", "messageId": "1414", "endLine": 260, "endColumn": 15}, {"ruleId": "1254", "severity": 1, "message": "1255", "line": 272, "column": 34, "nodeType": "1256", "messageId": "1257", "endLine": 272, "endColumn": 36}, {"ruleId": "1254", "severity": 1, "message": "1416", "line": 273, "column": 23, "nodeType": "1256", "messageId": "1257", "endLine": 273, "endColumn": 25}, {"ruleId": "1204", "severity": 1, "message": "1417", "line": 59, "column": 8, "nodeType": "1206", "endLine": 59, "endColumn": 10, "suggestions": "1418"}, {"ruleId": "1177", "severity": 1, "message": "1313", "line": 62, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 62, "endColumn": 23}, {"ruleId": "1204", "severity": 1, "message": "1419", "line": 88, "column": 8, "nodeType": "1206", "endLine": 88, "endColumn": 16, "suggestions": "1420"}, {"ruleId": "1177", "severity": 1, "message": "1313", "line": 92, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 92, "endColumn": 23}, {"ruleId": "1204", "severity": 1, "message": "1421", "line": 118, "column": 8, "nodeType": "1206", "endLine": 118, "endColumn": 16, "suggestions": "1422"}, {"ruleId": "1204", "severity": 1, "message": "1221", "line": 135, "column": 8, "nodeType": "1206", "endLine": 135, "endColumn": 10, "suggestions": "1423"}, {"ruleId": "1204", "severity": 1, "message": "1424", "line": 157, "column": 8, "nodeType": "1206", "endLine": 157, "endColumn": 10, "suggestions": "1425"}, {"ruleId": "1204", "severity": 1, "message": "1426", "line": 170, "column": 8, "nodeType": "1206", "endLine": 170, "endColumn": 10, "suggestions": "1427"}, {"ruleId": "1204", "severity": 1, "message": "1428", "line": 210, "column": 8, "nodeType": "1206", "endLine": 210, "endColumn": 10, "suggestions": "1429"}, {"ruleId": "1204", "severity": 1, "message": "1430", "line": 238, "column": 8, "nodeType": "1206", "endLine": 238, "endColumn": 10, "suggestions": "1431"}, {"ruleId": "1177", "severity": 1, "message": "1262", "line": 1, "column": 29, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 37}, {"ruleId": "1177", "severity": 1, "message": "1258", "line": 2, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1432", "line": 16, "column": 26, "nodeType": "1179", "messageId": "1180", "endLine": 16, "endColumn": 45}, {"ruleId": "1177", "severity": 1, "message": "1433", "line": 18, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 18, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1434", "line": 21, "column": 30, "nodeType": "1179", "messageId": "1180", "endLine": 21, "endColumn": 52}, {"ruleId": "1204", "severity": 1, "message": "1435", "line": 48, "column": 8, "nodeType": "1206", "endLine": 48, "endColumn": 25, "suggestions": "1436"}, {"ruleId": "1177", "severity": 1, "message": "1218", "line": 1, "column": 57, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 68}, {"ruleId": "1177", "severity": 1, "message": "1260", "line": 4, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 4, "endColumn": 16}, {"ruleId": "1177", "severity": 1, "message": "1437", "line": 4, "column": 18, "nodeType": "1179", "messageId": "1180", "endLine": 4, "endColumn": 22}, {"ruleId": "1177", "severity": 1, "message": "1276", "line": 7, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 7, "endColumn": 22}, {"ruleId": "1177", "severity": 1, "message": "1438", "line": 8, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1439", "line": 10, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 10, "endColumn": 22}, {"ruleId": "1177", "severity": 1, "message": "1440", "line": 11, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 11, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1441", "line": 13, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 13, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1195", "line": 14, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 14, "endColumn": 34}, {"ruleId": "1177", "severity": 1, "message": "1442", "line": 18, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 18, "endColumn": 12}, {"ruleId": "1177", "severity": 1, "message": "1443", "line": 21, "column": 26, "nodeType": "1179", "messageId": "1180", "endLine": 21, "endColumn": 40}, {"ruleId": "1177", "severity": 1, "message": "1444", "line": 33, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 33, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1200", "line": 60, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 60, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1445", "line": 61, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 61, "endColumn": 26}, {"ruleId": "1177", "severity": 1, "message": "1446", "line": 65, "column": 28, "nodeType": "1179", "messageId": "1180", "endLine": 65, "endColumn": 45}, {"ruleId": "1177", "severity": 1, "message": "1447", "line": 67, "column": 24, "nodeType": "1179", "messageId": "1180", "endLine": 67, "endColumn": 37}, {"ruleId": "1177", "severity": 1, "message": "1448", "line": 68, "column": 20, "nodeType": "1179", "messageId": "1180", "endLine": 68, "endColumn": 29}, {"ruleId": "1177", "severity": 1, "message": "1449", "line": 71, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 71, "endColumn": 20}, {"ruleId": "1177", "severity": 1, "message": "1450", "line": 71, "column": 22, "nodeType": "1179", "messageId": "1180", "endLine": 71, "endColumn": 32}, {"ruleId": "1177", "severity": 1, "message": "1451", "line": 71, "column": 34, "nodeType": "1179", "messageId": "1180", "endLine": 71, "endColumn": 43}, {"ruleId": "1177", "severity": 1, "message": "1452", "line": 71, "column": 45, "nodeType": "1179", "messageId": "1180", "endLine": 71, "endColumn": 57}, {"ruleId": "1177", "severity": 1, "message": "1453", "line": 71, "column": 59, "nodeType": "1179", "messageId": "1180", "endLine": 71, "endColumn": 68}, {"ruleId": "1177", "severity": 1, "message": "1454", "line": 71, "column": 70, "nodeType": "1179", "messageId": "1180", "endLine": 71, "endColumn": 82}, {"ruleId": "1177", "severity": 1, "message": "1455", "line": 71, "column": 84, "nodeType": "1179", "messageId": "1180", "endLine": 71, "endColumn": 98}, {"ruleId": "1177", "severity": 1, "message": "1283", "line": 71, "column": 100, "nodeType": "1179", "messageId": "1180", "endLine": 71, "endColumn": 108}, {"ruleId": "1177", "severity": 1, "message": "1278", "line": 71, "column": 110, "nodeType": "1179", "messageId": "1180", "endLine": 71, "endColumn": 128}, {"ruleId": "1177", "severity": 1, "message": "1456", "line": 71, "column": 130, "nodeType": "1179", "messageId": "1180", "endLine": 71, "endColumn": 143}, {"ruleId": "1177", "severity": 1, "message": "1457", "line": 71, "column": 145, "nodeType": "1179", "messageId": "1180", "endLine": 71, "endColumn": 161}, {"ruleId": "1177", "severity": 1, "message": "1458", "line": 71, "column": 163, "nodeType": "1179", "messageId": "1180", "endLine": 71, "endColumn": 175}, {"ruleId": "1177", "severity": 1, "message": "1282", "line": 71, "column": 177, "nodeType": "1179", "messageId": "1180", "endLine": 71, "endColumn": 190}, {"ruleId": "1177", "severity": 1, "message": "1191", "line": 72, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 72, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1192", "line": 72, "column": 27, "nodeType": "1179", "messageId": "1180", "endLine": 72, "endColumn": 42}, {"ruleId": "1177", "severity": 1, "message": "1203", "line": 72, "column": 58, "nodeType": "1179", "messageId": "1180", "endLine": 72, "endColumn": 73}, {"ruleId": "1177", "severity": 1, "message": "1235", "line": 73, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 73, "endColumn": 20}, {"ruleId": "1177", "severity": 1, "message": "1459", "line": 74, "column": 23, "nodeType": "1179", "messageId": "1180", "endLine": 74, "endColumn": 33}, {"ruleId": "1177", "severity": 1, "message": "1460", "line": 81, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 81, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1212", "line": 93, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 93, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1461", "line": 94, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 94, "endColumn": 17}, {"ruleId": "1204", "severity": 1, "message": "1462", "line": 137, "column": 7, "nodeType": "1206", "endLine": 137, "endColumn": 9, "suggestions": "1463"}, {"ruleId": "1204", "severity": 1, "message": "1464", "line": 151, "column": 7, "nodeType": "1206", "endLine": 151, "endColumn": 9, "suggestions": "1465"}, {"ruleId": "1204", "severity": 1, "message": "1466", "line": 166, "column": 8, "nodeType": "1206", "endLine": 166, "endColumn": 10, "suggestions": "1467"}, {"ruleId": "1204", "severity": 1, "message": "1468", "line": 209, "column": 8, "nodeType": "1206", "endLine": 209, "endColumn": 24, "suggestions": "1469"}, {"ruleId": "1204", "severity": 1, "message": "1470", "line": 217, "column": 8, "nodeType": "1206", "endLine": 217, "endColumn": 10, "suggestions": "1471"}, {"ruleId": "1177", "severity": 1, "message": "1472", "line": 240, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 240, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1473", "line": 17, "column": 68, "nodeType": "1179", "messageId": "1180", "endLine": 17, "endColumn": 86}, {"ruleId": "1177", "severity": 1, "message": "1474", "line": 17, "column": 88, "nodeType": "1179", "messageId": "1180", "endLine": 17, "endColumn": 97}, {"ruleId": "1177", "severity": 1, "message": "1458", "line": 17, "column": 99, "nodeType": "1179", "messageId": "1180", "endLine": 17, "endColumn": 111}, {"ruleId": "1177", "severity": 1, "message": "1282", "line": 17, "column": 154, "nodeType": "1179", "messageId": "1180", "endLine": 17, "endColumn": 167}, {"ruleId": "1177", "severity": 1, "message": "1475", "line": 27, "column": 25, "nodeType": "1179", "messageId": "1180", "endLine": 27, "endColumn": 39}, {"ruleId": "1204", "severity": 1, "message": "1476", "line": 35, "column": 8, "nodeType": "1206", "endLine": 35, "endColumn": 16, "suggestions": "1477"}, {"ruleId": "1204", "severity": 1, "message": "1478", "line": 55, "column": 8, "nodeType": "1206", "endLine": 55, "endColumn": 16, "suggestions": "1479"}, {"ruleId": "1204", "severity": 1, "message": "1480", "line": 82, "column": 8, "nodeType": "1206", "endLine": 82, "endColumn": 23, "suggestions": "1481"}, {"ruleId": "1177", "severity": 1, "message": "1482", "line": 5, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 5, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1483", "line": 7, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 7, "endColumn": 21}, {"ruleId": "1177", "severity": 1, "message": "1484", "line": 11, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 11, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1485", "line": 21, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 21, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1486", "line": 29, "column": 24, "nodeType": "1179", "messageId": "1180", "endLine": 29, "endColumn": 36}, {"ruleId": "1177", "severity": 1, "message": "1487", "line": 35, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 35, "endColumn": 27}, {"ruleId": "1177", "severity": 1, "message": "1488", "line": 36, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 36, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1489", "line": 37, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 37, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1490", "line": 38, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 38, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1491", "line": 39, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 39, "endColumn": 28}, {"ruleId": "1177", "severity": 1, "message": "1287", "line": 40, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 40, "endColumn": 22}, {"ruleId": "1177", "severity": 1, "message": "1192", "line": 42, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 42, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1332", "line": 43, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 43, "endColumn": 29}, {"ruleId": "1177", "severity": 1, "message": "1492", "line": 45, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 45, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1493", "line": 52, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 52, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1456", "line": 52, "column": 26, "nodeType": "1179", "messageId": "1180", "endLine": 52, "endColumn": 39}, {"ruleId": "1177", "severity": 1, "message": "1494", "line": 52, "column": 41, "nodeType": "1179", "messageId": "1180", "endLine": 52, "endColumn": 59}, {"ruleId": "1177", "severity": 1, "message": "1495", "line": 52, "column": 61, "nodeType": "1179", "messageId": "1180", "endLine": 52, "endColumn": 81}, {"ruleId": "1177", "severity": 1, "message": "1496", "line": 52, "column": 83, "nodeType": "1179", "messageId": "1180", "endLine": 52, "endColumn": 101}, {"ruleId": "1177", "severity": 1, "message": "1383", "line": 52, "column": 103, "nodeType": "1179", "messageId": "1180", "endLine": 52, "endColumn": 124}, {"ruleId": "1177", "severity": 1, "message": "1384", "line": 52, "column": 126, "nodeType": "1179", "messageId": "1180", "endLine": 52, "endColumn": 149}, {"ruleId": "1177", "severity": 1, "message": "1385", "line": 52, "column": 151, "nodeType": "1179", "messageId": "1180", "endLine": 52, "endColumn": 172}, {"ruleId": "1177", "severity": 1, "message": "1497", "line": 52, "column": 174, "nodeType": "1179", "messageId": "1180", "endLine": 52, "endColumn": 179}, {"ruleId": "1177", "severity": 1, "message": "1282", "line": 52, "column": 181, "nodeType": "1179", "messageId": "1180", "endLine": 52, "endColumn": 194}, {"ruleId": "1177", "severity": 1, "message": "1200", "line": 55, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 55, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1460", "line": 58, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 58, "endColumn": 19}, {"ruleId": "1204", "severity": 1, "message": "1498", "line": 170, "column": 12, "nodeType": "1206", "endLine": 170, "endColumn": 26, "suggestions": "1499"}, {"ruleId": "1177", "severity": 1, "message": "1500", "line": 18, "column": 27, "nodeType": "1179", "messageId": "1180", "endLine": 18, "endColumn": 40}, {"ruleId": "1177", "severity": 1, "message": "1282", "line": 18, "column": 54, "nodeType": "1179", "messageId": "1180", "endLine": 18, "endColumn": 67}, {"ruleId": "1177", "severity": 1, "message": "1456", "line": 18, "column": 76, "nodeType": "1179", "messageId": "1180", "endLine": 18, "endColumn": 89}, {"ruleId": "1177", "severity": 1, "message": "1487", "line": 19, "column": 63, "nodeType": "1179", "messageId": "1180", "endLine": 19, "endColumn": 81}, {"ruleId": "1177", "severity": 1, "message": "1339", "line": 29, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 29, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1501", "line": 30, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 30, "endColumn": 35}, {"ruleId": "1204", "severity": 1, "message": "1214", "line": 58, "column": 8, "nodeType": "1206", "endLine": 58, "endColumn": 15, "suggestions": "1502"}, {"ruleId": "1204", "severity": 1, "message": "1503", "line": 66, "column": 8, "nodeType": "1206", "endLine": 66, "endColumn": 15, "suggestions": "1504"}, {"ruleId": "1204", "severity": 1, "message": "1407", "line": 71, "column": 8, "nodeType": "1206", "endLine": 71, "endColumn": 23, "suggestions": "1505"}, {"ruleId": "1204", "severity": 1, "message": "1478", "line": 76, "column": 8, "nodeType": "1206", "endLine": 76, "endColumn": 16, "suggestions": "1506"}, {"ruleId": "1177", "severity": 1, "message": "1287", "line": 6, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 6, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1507", "line": 6, "column": 64, "nodeType": "1179", "messageId": "1180", "endLine": 6, "endColumn": 81}, {"ruleId": "1177", "severity": 1, "message": "1332", "line": 6, "column": 83, "nodeType": "1179", "messageId": "1180", "endLine": 6, "endColumn": 103}, {"ruleId": "1177", "severity": 1, "message": "1508", "line": 7, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 7, "endColumn": 26}, {"ruleId": "1177", "severity": 1, "message": "1213", "line": 1, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1262", "line": 1, "column": 21, "nodeType": "1179", "messageId": "1180", "endLine": 1, "endColumn": 29}, {"ruleId": "1177", "severity": 1, "message": "1509", "line": 3, "column": 122, "nodeType": "1179", "messageId": "1180", "endLine": 3, "endColumn": 141}, {"ruleId": "1177", "severity": 1, "message": "1277", "line": 5, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 5, "endColumn": 13}, {"ruleId": "1177", "severity": 1, "message": "1510", "line": 9, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 9, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1195", "line": 9, "column": 26, "nodeType": "1179", "messageId": "1180", "endLine": 9, "endColumn": 50}, {"ruleId": "1177", "severity": 1, "message": "1483", "line": 11, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 11, "endColumn": 21}, {"ruleId": "1177", "severity": 1, "message": "1511", "line": 30, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 30, "endColumn": 23}, {"ruleId": "1177", "severity": 1, "message": "1338", "line": 41, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 41, "endColumn": 16}, {"ruleId": "1177", "severity": 1, "message": "1331", "line": 46, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 46, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1512", "line": 46, "column": 26, "nodeType": "1179", "messageId": "1180", "endLine": 46, "endColumn": 41}, {"ruleId": "1177", "severity": 1, "message": "1513", "line": 48, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 48, "endColumn": 28}, {"ruleId": "1177", "severity": 1, "message": "1284", "line": 53, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 53, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1203", "line": 53, "column": 26, "nodeType": "1179", "messageId": "1180", "endLine": 53, "endColumn": 41}, {"ruleId": "1177", "severity": 1, "message": "1339", "line": 57, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 57, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1514", "line": 5, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 5, "endColumn": 28}, {"ruleId": "1177", "severity": 1, "message": "1515", "line": 5, "column": 30, "nodeType": "1179", "messageId": "1180", "endLine": 5, "endColumn": 43}, {"ruleId": "1177", "severity": 1, "message": "1258", "line": 8, "column": 21, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 29}, {"ruleId": "1177", "severity": 1, "message": "1400", "line": 19, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 19, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1460", "line": 59, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 59, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1382", "line": 66, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 66, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1516", "line": 73, "column": 12, "nodeType": "1179", "messageId": "1180", "endLine": 73, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1517", "line": 80, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 80, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1518", "line": 22, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 22, "endColumn": 21}, {"ruleId": "1204", "severity": 1, "message": "1519", "line": 86, "column": 8, "nodeType": "1206", "endLine": 86, "endColumn": 56, "suggestions": "1520"}, {"ruleId": "1177", "severity": 1, "message": "1514", "line": 5, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 5, "endColumn": 28}, {"ruleId": "1177", "severity": 1, "message": "1258", "line": 8, "column": 21, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 29}, {"ruleId": "1177", "severity": 1, "message": "1521", "line": 8, "column": 31, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 41}, {"ruleId": "1177", "severity": 1, "message": "1295", "line": 9, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 9, "endColumn": 20}, {"ruleId": "1177", "severity": 1, "message": "1522", "line": 10, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 10, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1523", "line": 10, "column": 26, "nodeType": "1179", "messageId": "1180", "endLine": 10, "endColumn": 39}, {"ruleId": "1177", "severity": 1, "message": "1524", "line": 10, "column": 41, "nodeType": "1179", "messageId": "1180", "endLine": 10, "endColumn": 55}, {"ruleId": "1177", "severity": 1, "message": "1525", "line": 10, "column": 57, "nodeType": "1179", "messageId": "1180", "endLine": 10, "endColumn": 69}, {"ruleId": "1177", "severity": 1, "message": "1438", "line": 11, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 11, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1526", "line": 12, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 12, "endColumn": 22}, {"ruleId": "1177", "severity": 1, "message": "1527", "line": 13, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 13, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1276", "line": 14, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 14, "endColumn": 22}, {"ruleId": "1177", "severity": 1, "message": "1296", "line": 16, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 16, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1297", "line": 49, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 49, "endColumn": 27}, {"ruleId": "1177", "severity": 1, "message": "1298", "line": 55, "column": 7, "nodeType": "1179", "messageId": "1180", "endLine": 55, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1449", "line": 71, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 71, "endColumn": 12}, {"ruleId": "1177", "severity": 1, "message": "1460", "line": 77, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 77, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1528", "line": 81, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 81, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1529", "line": 81, "column": 21, "nodeType": "1179", "messageId": "1180", "endLine": 81, "endColumn": 33}, {"ruleId": "1177", "severity": 1, "message": "1530", "line": 83, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 83, "endColumn": 20}, {"ruleId": "1177", "severity": 1, "message": "1280", "line": 83, "column": 22, "nodeType": "1179", "messageId": "1180", "endLine": 83, "endColumn": 35}, {"ruleId": "1177", "severity": 1, "message": "1531", "line": 85, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 85, "endColumn": 21}, {"ruleId": "1177", "severity": 1, "message": "1532", "line": 85, "column": 23, "nodeType": "1179", "messageId": "1180", "endLine": 85, "endColumn": 37}, {"ruleId": "1177", "severity": 1, "message": "1281", "line": 87, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 87, "endColumn": 26}, {"ruleId": "1177", "severity": 1, "message": "1284", "line": 88, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 88, "endColumn": 22}, {"ruleId": "1177", "severity": 1, "message": "1203", "line": 88, "column": 24, "nodeType": "1179", "messageId": "1180", "endLine": 88, "endColumn": 39}, {"ruleId": "1177", "severity": 1, "message": "1285", "line": 89, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 89, "endColumn": 27}, {"ruleId": "1177", "severity": 1, "message": "1533", "line": 89, "column": 29, "nodeType": "1179", "messageId": "1180", "endLine": 89, "endColumn": 49}, {"ruleId": "1177", "severity": 1, "message": "1286", "line": 90, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 90, "endColumn": 29}, {"ruleId": "1177", "severity": 1, "message": "1534", "line": 90, "column": 31, "nodeType": "1179", "messageId": "1180", "endLine": 90, "endColumn": 53}, {"ruleId": "1177", "severity": 1, "message": "1283", "line": 91, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 91, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1535", "line": 91, "column": 20, "nodeType": "1179", "messageId": "1180", "endLine": 91, "endColumn": 31}, {"ruleId": "1177", "severity": 1, "message": "1536", "line": 92, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 92, "endColumn": 20}, {"ruleId": "1177", "severity": 1, "message": "1212", "line": 96, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 96, "endColumn": 23}, {"ruleId": "1177", "severity": 1, "message": "1517", "line": 99, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 99, "endColumn": 23}, {"ruleId": "1177", "severity": 1, "message": "1537", "line": 228, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 228, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1538", "line": 234, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 234, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1539", "line": 240, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 240, "endColumn": 27}, {"ruleId": "1177", "severity": 1, "message": "1518", "line": 20, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 20, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1540", "line": 21, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 21, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1518", "line": 39, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 39, "endColumn": 21}, {"ruleId": "1204", "severity": 1, "message": "1519", "line": 120, "column": 8, "nodeType": "1206", "endLine": 120, "endColumn": 56, "suggestions": "1541"}, {"ruleId": "1177", "severity": 1, "message": "1518", "line": 22, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 22, "endColumn": 21}, {"ruleId": "1177", "severity": 1, "message": "1235", "line": 34, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 34, "endColumn": 20}, {"ruleId": "1204", "severity": 1, "message": "1519", "line": 84, "column": 8, "nodeType": "1206", "endLine": 84, "endColumn": 56, "suggestions": "1542"}, {"ruleId": "1177", "severity": 1, "message": "1543", "line": 6, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 6, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1229", "line": 11, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 11, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1518", "line": 26, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 26, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1235", "line": 43, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 43, "endColumn": 18}, {"ruleId": "1204", "severity": 1, "message": "1519", "line": 112, "column": 6, "nodeType": "1206", "endLine": 112, "endColumn": 54, "suggestions": "1544"}, {"ruleId": "1177", "severity": 1, "message": "1545", "line": 7, "column": 3, "nodeType": "1179", "messageId": "1180", "endLine": 7, "endColumn": 12}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 56, "column": 6, "nodeType": "1206", "endLine": 56, "endColumn": 24, "suggestions": "1547"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 64, "column": 6, "nodeType": "1206", "endLine": 64, "endColumn": 24, "suggestions": "1548"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 77, "column": 6, "nodeType": "1206", "endLine": 77, "endColumn": 24, "suggestions": "1549"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 90, "column": 6, "nodeType": "1206", "endLine": 90, "endColumn": 24, "suggestions": "1550"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 103, "column": 6, "nodeType": "1206", "endLine": 103, "endColumn": 24, "suggestions": "1551"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 116, "column": 6, "nodeType": "1206", "endLine": 116, "endColumn": 24, "suggestions": "1552"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 129, "column": 6, "nodeType": "1206", "endLine": 129, "endColumn": 24, "suggestions": "1553"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 142, "column": 6, "nodeType": "1206", "endLine": 142, "endColumn": 24, "suggestions": "1554"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 173, "column": 6, "nodeType": "1206", "endLine": 173, "endColumn": 24, "suggestions": "1555"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 187, "column": 6, "nodeType": "1206", "endLine": 187, "endColumn": 24, "suggestions": "1556"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 200, "column": 6, "nodeType": "1206", "endLine": 200, "endColumn": 24, "suggestions": "1557"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 213, "column": 6, "nodeType": "1206", "endLine": 213, "endColumn": 24, "suggestions": "1558"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 226, "column": 6, "nodeType": "1206", "endLine": 226, "endColumn": 24, "suggestions": "1559"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 239, "column": 6, "nodeType": "1206", "endLine": 239, "endColumn": 24, "suggestions": "1560"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 252, "column": 6, "nodeType": "1206", "endLine": 252, "endColumn": 24, "suggestions": "1561"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 265, "column": 6, "nodeType": "1206", "endLine": 265, "endColumn": 24, "suggestions": "1562"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 278, "column": 6, "nodeType": "1206", "endLine": 278, "endColumn": 24, "suggestions": "1563"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 291, "column": 6, "nodeType": "1206", "endLine": 291, "endColumn": 24, "suggestions": "1564"}, {"ruleId": "1177", "severity": 1, "message": "1565", "line": 296, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 296, "endColumn": 36}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 304, "column": 6, "nodeType": "1206", "endLine": 304, "endColumn": 24, "suggestions": "1566"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 317, "column": 6, "nodeType": "1206", "endLine": 317, "endColumn": 24, "suggestions": "1567"}, {"ruleId": "1204", "severity": 1, "message": "1568", "line": 343, "column": 6, "nodeType": "1206", "endLine": 343, "endColumn": 24, "suggestions": "1569"}, {"ruleId": "1204", "severity": 1, "message": "1546", "line": 458, "column": 6, "nodeType": "1206", "endLine": 458, "endColumn": 14, "suggestions": "1570"}, {"ruleId": "1177", "severity": 1, "message": "1276", "line": 5, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 5, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1571", "line": 6, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 6, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1514", "line": 7, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 7, "endColumn": 23}, {"ruleId": "1177", "severity": 1, "message": "1572", "line": 8, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1545", "line": 9, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 9, "endColumn": 14}, {"ruleId": "1177", "severity": 1, "message": "1573", "line": 10, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 10, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1515", "line": 11, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 11, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1574", "line": 12, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 12, "endColumn": 15}, {"ruleId": "1177", "severity": 1, "message": "1509", "line": 16, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 16, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1575", "line": 17, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 17, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1576", "line": 18, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 18, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1577", "line": 19, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 19, "endColumn": 20}, {"ruleId": "1177", "severity": 1, "message": "1258", "line": 20, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 20, "endColumn": 13}, {"ruleId": "1177", "severity": 1, "message": "1261", "line": 21, "column": 5, "nodeType": "1179", "messageId": "1180", "endLine": 21, "endColumn": 10}, {"ruleId": "1177", "severity": 1, "message": "1578", "line": 25, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 25, "endColumn": 19}, {"ruleId": "1254", "severity": 1, "message": "1255", "line": 135, "column": 34, "nodeType": "1256", "messageId": "1257", "endLine": 135, "endColumn": 36}, {"ruleId": "1204", "severity": 1, "message": "1579", "line": 142, "column": 8, "nodeType": "1206", "endLine": 142, "endColumn": 10, "suggestions": "1580"}, {"ruleId": "1204", "severity": 1, "message": "1581", "line": 216, "column": 8, "nodeType": "1206", "endLine": 216, "endColumn": 10, "suggestions": "1582"}, {"ruleId": "1177", "severity": 1, "message": "1583", "line": 6, "column": 3, "nodeType": "1179", "messageId": "1180", "endLine": 6, "endColumn": 23}, {"ruleId": "1177", "severity": 1, "message": "1584", "line": 10, "column": 3, "nodeType": "1179", "messageId": "1180", "endLine": 10, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1585", "line": 5, "column": 3, "nodeType": "1179", "messageId": "1180", "endLine": 5, "endColumn": 6}, {"ruleId": "1177", "severity": 1, "message": "1586", "line": 9, "column": 3, "nodeType": "1179", "messageId": "1180", "endLine": 9, "endColumn": 7}, {"ruleId": "1177", "severity": 1, "message": "1587", "line": 11, "column": 3, "nodeType": "1179", "messageId": "1180", "endLine": 11, "endColumn": 20}, {"ruleId": "1177", "severity": 1, "message": "1588", "line": 5, "column": 3, "nodeType": "1179", "messageId": "1180", "endLine": 5, "endColumn": 15}, {"ruleId": "1177", "severity": 1, "message": "1217", "line": 2, "column": 49, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 55}, {"ruleId": "1177", "severity": 1, "message": "1440", "line": 23, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 23, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1441", "line": 25, "column": 8, "nodeType": "1179", "messageId": "1180", "endLine": 25, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1589", "line": 43, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 43, "endColumn": 15}, {"ruleId": "1177", "severity": 1, "message": "1590", "line": 46, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 46, "endColumn": 18}, {"ruleId": "1177", "severity": 1, "message": "1540", "line": 50, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 50, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1338", "line": 56, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 56, "endColumn": 16}, {"ruleId": "1177", "severity": 1, "message": "1591", "line": 65, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 65, "endColumn": 21}, {"ruleId": "1177", "severity": 1, "message": "1592", "line": 66, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 66, "endColumn": 35}, {"ruleId": "1177", "severity": 1, "message": "1517", "line": 71, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 71, "endColumn": 21}, {"ruleId": "1177", "severity": 1, "message": "1593", "line": 147, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 147, "endColumn": 29}, {"ruleId": "1177", "severity": 1, "message": "1594", "line": 177, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 177, "endColumn": 26}, {"ruleId": "1177", "severity": 1, "message": "1406", "line": 19, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 19, "endColumn": 20}, {"ruleId": "1177", "severity": 1, "message": "1595", "line": 9, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 9, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1596", "line": 9, "column": 19, "nodeType": "1179", "messageId": "1180", "endLine": 9, "endColumn": 26}, {"ruleId": "1177", "severity": 1, "message": "1597", "line": 9, "column": 28, "nodeType": "1179", "messageId": "1180", "endLine": 9, "endColumn": 37}, {"ruleId": "1177", "severity": 1, "message": "1449", "line": 21, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 21, "endColumn": 16}, {"ruleId": "1177", "severity": 1, "message": "1598", "line": 37, "column": 13, "nodeType": "1179", "messageId": "1180", "endLine": 37, "endColumn": 31}, {"ruleId": "1177", "severity": 1, "message": "1599", "line": 108, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 108, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1216", "line": 2, "column": 38, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 47}, {"ruleId": "1177", "severity": 1, "message": "1338", "line": 17, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 17, "endColumn": 14}, {"ruleId": "1177", "severity": 1, "message": "1589", "line": 23, "column": 11, "nodeType": "1179", "messageId": "1180", "endLine": 23, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1449", "line": 23, "column": 19, "nodeType": "1179", "messageId": "1180", "endLine": 23, "endColumn": 26}, {"ruleId": "1177", "severity": 1, "message": "1217", "line": 2, "column": 49, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 55}, {"ruleId": "1177", "severity": 1, "message": "1218", "line": 2, "column": 57, "nodeType": "1179", "messageId": "1180", "endLine": 2, "endColumn": 68}, {"ruleId": "1177", "severity": 1, "message": "1263", "line": 10, "column": 10, "nodeType": "1179", "messageId": "1180", "endLine": 10, "endColumn": 23}, {"ruleId": "1177", "severity": 1, "message": "1241", "line": 25, "column": 9, "nodeType": "1179", "messageId": "1180", "endLine": 25, "endColumn": 16}, {"ruleId": "1177", "severity": 1, "message": "1600", "line": 6, "column": 3, "nodeType": "1179", "messageId": "1180", "endLine": 6, "endColumn": 21}, {"ruleId": "1177", "severity": 1, "message": "1601", "line": 8, "column": 3, "nodeType": "1179", "messageId": "1180", "endLine": 8, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1602", "line": 9, "column": 3, "nodeType": "1179", "messageId": "1180", "endLine": 9, "endColumn": 19}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'useAuth' is defined but never used.", "'getAuth' is defined but never used.", "'signInWithEmailAndPassword' is defined but never used.", "'signOut' is defined but never used.", "'onAuthStateChanged' is defined but never used.", "'axiosInstance' is assigned a value but never used.", "'setAxiosInstance' is assigned a value but never used.", "'user' is assigned a value but never used.", "'setUser' is assigned a value but never used.", "'saveToken' is assigned a value but never used.", "'playerScores' is assigned a value but never used.", "'setPlayerScores' is assigned a value but never used.", "'sendGridToPlayers' is defined but never used.", "'setCurrentChunk' is defined but never used.", "'setCurrentPacketQuestion' is defined but never used.", "'round' is defined but never used.", "'setSelectedPacketToPlayer' is defined but never used.", "'testId' is assigned a value but never used.", "'sounds' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'timeLeft' is assigned a value but never used.", "'setTimeLeft' is assigned a value but never used.", "'setAnimationKey' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'hostRoomId'. Either include it or remove the dependency array.", "ArrayExpression", ["1603"], "'useHost' is defined but never used.", "'roundRef' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleTimeEnd'. Either include it or remove the dependency array.", ["1604"], "'isInitialMount' is assigned a value but never used.", "'authService' is defined but never used.", "React Hook useEffect has a missing dependency: 'roomId'. Either include it or remove the dependency array.", ["1605"], "'ReactNode' is defined but never used.", "'useRef' is defined but never used.", "'useCallback' is defined but never used.", "React Hook useEffect has missing dependencies: 'roomId' and 'testName'. Either include them or remove the dependency array.", ["1606"], "React Hook useEffect has missing dependencies: 'roomId' and 'sounds'. Either include them or remove the dependency array.", ["1607"], "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'isAllowed' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isSpectator', 'navigate', 'round', and 'setInitialGrid'. Either include them or remove the dependency array.", ["1608"], "'ReactPlaceholder' is defined but never used.", "'listenToBuzzing' is defined but never used.", "'buzzedPlayer' is assigned a value but never used.", "'setBuzzedPlayer' is assigned a value but never used.", "'showModal' is assigned a value but never used.", "'setShowModal' is assigned a value but never used.", "'isMounted' is assigned a value but never used.", ["1609"], ["1610"], "'joinRoom' is defined but never used.", "'Round4' is defined but never used.", "'exampleGrid' is assigned a value but never used.", "'loading' is assigned a value but never used.", ["1611"], "React Hook useEffect has a missing dependency: 'images.length'. Either include it or remove the dependency array.", ["1612"], ["1613"], "'banner' is defined but never used.", "'card' is defined but never used.", "'Header' is defined but never used.", "'Round1' is defined but never used.", "'QuestionBoxRound1' is defined but never used.", "'Play' is defined but never used.", ["1614"], "'getToken' is assigned a value but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "'Question' is defined but never used.", "'getAxiosAuthContext' is defined but never used.", "'Answer' is defined but never used.", "'Score' is defined but never used.", "'useState' is defined but never used.", "'updateHistory' is defined but never used.", "'useDispatch' is defined but never used.", "'useSelector' is defined but never used.", "'AuthState' is defined but never used.", "'AuthUser' is defined but never used.", "'UserProfile' is defined but never used.", "'useAppDispatch' is defined but never used.", "React Hook useEffect has a missing dependency: 'authenticateUser'. Either include it or remove the dependency array.", ["1615"], "'errorCode' is assigned a value but never used.", "'notifyBackendFileUploaded' is defined but never used.", "'RoundBase' is defined but never used.", "'GameState' is defined but never used.", "'submitAnswer' is defined but never used.", "'set' is defined but never used.", "'setCurrentQuestion' is assigned a value but never used.", "'setCorrectAnswer' is assigned a value but never used.", "'setIsExpanded' is assigned a value but never used.", "'playerAnswerTime' is assigned a value but never used.", "'setAnswerList' is assigned a value but never used.", "'position' is assigned a value but never used.", "'animationKey' is assigned a value but never used.", "'currentPlayerName' is assigned a value but never used.", "'currentPlayerAvatar' is assigned a value but never used.", "'currentAnswer' is assigned a value but never used.", "'listenToCurrentQuestion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'playerAnswerRef'. Either include it or remove the dependency array. Mutable values like 'playerAnswerRef.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["1616"], "React Hook useEffect has missing dependencies: 'listenToTimeStart' and 'startTimer'. Either include them or remove the dependency array.", ["1617"], "React Hook useEffect has missing dependencies: 'deletePath' and 'listenToSound'. Either include them or remove the dependency array.", ["1618"], "'renderGrid' is defined but never used.", "'HintWord' is defined but never used.", "'QuestionBoxProps' is defined but never used.", "'mainKeyword' is assigned a value but never used.", "'hintWordsLength' is assigned a value but never used.", "'setHintWordsLength' is assigned a value but never used.", "'markedCharacters' is assigned a value but never used.", "'highlightedCharacters' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentPlayerAvatar', 'currentPlayerName', 'isHost', 'isSpectator', 'playerAnswerRef', 'playerAnswerTime', 'position', 'roomId', and 'setAnimationKey'. Either include them or remove the dependency array.", ["1619"], "React Hook useEffect has missing dependencies: 'roomId' and 'startTimer'. Either include them or remove the dependency array.", ["1620"], "'topBound' is assigned a value but never used.", "'bottomBound' is assigned a value but never used.", "array-callback-return", "Array.prototype.map() expects a return value from arrow function.", "ArrowFunctionExpression", "expectedInside", "'hasMounted' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'sounds'. Either include it or remove the dependency array.", ["1621"], ["1622"], "React Hook useEffect has missing dependencies: 'isHost' and 'setInitialGrid'. Either include them or remove the dependency array.", ["1623"], "React Hook useEffect has a missing dependency: 'revealCellsForPlayer'. Either include it or remove the dependency array.", ["1624"], "React Hook useEffect has missing dependencies: 'roomId' and 'setAnswerList'. Either include them or remove the dependency array.", ["1625"], "React Hook useEffect has missing dependencies: 'revealCellsForPlayer' and 'setAnswerList'. Either include them or remove the dependency array.", ["1626"], "React Hook useEffect has missing dependencies: 'revealCellsForPlayer' and 'sounds'. Either include them or remove the dependency array.", ["1627"], ["1628"], "'listenToCurrentQuestionsNumber' is defined but never used.", "'sendCorrectAnswer' is defined but never used.", "'MAX_PACKET_QUESTION' is assigned a value but never used.", "'hiddenTopics' is assigned a value but never used.", "'currentQuestionIndex' is assigned a value but never used.", "'inGameQuestionIndex' is assigned a value but never used.", "'playerCurrentQuestionIndex' is assigned a value but never used.", "'tempQuestionListRef' is assigned a value but never used.", "'tempQuestionList' is assigned a value but never used.", "'setTempQuestionList' is assigned a value but never used.", "'round' is assigned a value but never used.", "'isFirstMounted' is assigned a value but never used.", "'decodeQuestion' is defined but never used.", ["1629"], "'timeOut' is assigned a value but never used.", ["1630"], "React Hook useEffect has missing dependencies: 'setCurrentQuestion' and 'setSelectedTopic'. Either include them or remove the dependency array.", ["1631"], ["1632"], "React Hook useEffect has a missing dependency: 'setAnimationKey'. Either include it or remove the dependency array.", ["1633"], "React Hook useEffect has missing dependencies: 'isHost', 'roomId', and 'testName'. Either include them or remove the dependency array.", ["1634"], ["1635"], "React Hook useEffect has missing dependencies: 'isHost', 'roomId', and 'setSelectedTopic'. Either include them or remove the dependency array.", ["1636"], "React Hook useEffect has missing dependencies: 'currentAnswer', 'isHost', 'roomId', 'setAnswerList', and 'setCurrentQuestion'. Either include them or remove the dependency array.", ["1637"], "'uploadTestToServer' is defined but never used.", "'response' is assigned a value but never used.", "'getTest' is defined but never used.", "'getTestByUserId' is defined but never used.", "'addNewQuestion' is defined but never used.", "'useQuery' is defined but never used.", "'handleEditClick' is assigned a value but never used.", "'handleAddQuestion' is assigned a value but never used.", ["1638"], ["1639"], ["1640"], "React Hook useEffect has missing dependencies: 'currentAnswer', 'isHost', 'roomId', and 'setAnswerList'. Either include them or remove the dependency array.", ["1641"], ["1642"], ["1643"], "'deletePath' is defined but never used.", "'listenToSound' is defined but never used.", "'listenToQuestions' is defined but never used.", "'listenToSelectedCell' is defined but never used.", "'listenToCellColor' is defined but never used.", "'listenToAnswers' is defined but never used.", "'listenToStar' is defined but never used.", "'colorMap' is assigned a value but never used.", "'selectedQuestion' is assigned a value but never used.", "'setSelectedQuestion' is assigned a value but never used.", "'setMenu' is assigned a value but never used.", "'selectedCell' is assigned a value but never used.", "'setEasyQuestionNumber' is assigned a value but never used.", "'setMediumQuestionNumber' is assigned a value but never used.", "'setHardQuestionNumber' is assigned a value but never used.", "'setLevel' is assigned a value but never used.", ["1644"], ["1645"], "'updateQuestion' is defined but never used.", "'uploadFile' is defined but never used.", "'testManageMentService' is defined but never used.", "'RoomScoreTableProps' is defined but never used.", ["1646"], ["1647"], ["1648"], ["1649"], ["1650"], ["1651"], "'listenToTimeStart' is defined but never used.", "'GameGridProps' is defined but never used.", "'exampleQuestions' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'initialGrid' and 'setInitialGrid'. Either include them or remove the dependency array. If 'setGrid' needs the current value of 'initialGrid', you can also switch to useReducer instead of useState and read 'initialGrid' in the reducer.", ["1652"], ["1653"], "'lastBuzzedPlayerRef' is assigned a value but never used.", "'prevOrder' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'listenToScores'. Either include it or remove the dependency array.", ["1654"], "'uploadedFile' is assigned a value but never used.", "'CleanVars' is defined but never used.", "'curMatch' is defined but never used.", "@typescript-eslint/no-redeclare", "'xIndex' is already defined.", "redeclared", "'yIndex' is already defined.", "Expected '!==' and instead saw '!='.", "React Hook useEffect has missing dependencies: 'roomId' and 'startTimer'. Either include them or remove the dependency array. If 'startTimer' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1655"], "React Hook useEffect has missing dependencies: 'setBuzzedPlayer', 'setShowModal', and 'sounds'. Either include them or remove the dependency array. If 'setBuzzedPlayer' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1656"], "React Hook useEffect has missing dependencies: 'setShowModal', 'setStaredPlayer', and 'sounds'. Either include them or remove the dependency array. If 'setStaredPlayer' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1657"], ["1658"], "React Hook useEffect has missing dependencies: 'roomId', 'setCorrectAnswer', and 'sounds'. Either include them or remove the dependency array. If 'setCorrectAnswer' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1659"], "React Hook useEffect has missing dependencies: 'roomId', 'setCorrectAnswer', and 'setCurrentQuestion'. Either include them or remove the dependency array. If 'setCurrentQuestion' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1660"], "React Hook useEffect has missing dependencies: 'roomId', 'setGridColors', and 'setSelectedCell'. Either include them or remove the dependency array. If 'setGridColors' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1661"], "React Hook useEffect has missing dependencies: 'colorMap', 'roomId', 'setGrid', and 'setGridColors'. Either include them or remove the dependency array. If 'setGridColors' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1662"], "'setPlayerAnswerTime' is assigned a value but never used.", "'currentPlayer' is assigned a value but never used.", "'contextPlayerAnswerRef' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["1663"], "'User' is defined but never used.", "'getNextQuestion' is defined but never used.", "'HostManagement' is defined but never used.", "'PlayerScore' is defined but never used.", "'HostScore' is defined but never used.", "'EyeIcon' is defined but never used.", "'useAppSelector' is defined but never used.", "'Player' is defined but never used.", "'playerAnswerRef' is assigned a value but never used.", "'setSpectatorCount' is assigned a value but never used.", "'setRulesRound' is assigned a value but never used.", "'setUserId' is assigned a value but never used.", "'players' is assigned a value but never used.", "'setPlayers' is assigned a value but never used.", "'setRoomId' is assigned a value but never used.", "'playersArray' is assigned a value but never used.", "'roomRules' is assigned a value but never used.", "'setRoomRules' is assigned a value but never used.", "'setPlayerArray' is assigned a value but never used.", "'selectedTopic' is assigned a value but never used.", "'setSelectedTopic' is assigned a value but never used.", "'setScoreList' is assigned a value but never used.", "'startTimer' is assigned a value but never used.", "'testName' is assigned a value but never used.", "'styles' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'listenToNewPlayer'. Either include it or remove the dependency array.", ["1664"], "React Hook useEffect has missing dependencies: 'dispatch', 'isHost', and 'listenToCurrentQuestion'. Either include them or remove the dependency array.", ["1665"], "React Hook useEffect has missing dependencies: 'listenToCorrectAnswer' and 'sounds'. Either include them or remove the dependency array.", ["1666"], "React Hook useEffect has a missing dependency: 'setupDisconnect'. Either include it or remove the dependency array.", ["1667"], "React Hook useEffect has a missing dependency: 'listenToSpectatorJoin'. Either include it or remove the dependency array.", ["1668"], "'isFull' is assigned a value but never used.", "'triggerPlayerFlash' is assigned a value but never used.", "'scoreList' is assigned a value but never used.", "'setCurrentTurn' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'listenToCurrentTurn'. Either include it or remove the dependency array.", ["1669"], "React Hook useEffect has a missing dependency: 'listenToBroadcastedAnswer'. Either include it or remove the dependency array.", ["1670"], "React Hook useEffect has missing dependencies: 'listenToOpenBuzz' and 'sounds'. Either include them or remove the dependency array.", ["1671"], "'openBuzz' is defined but never used.", "'updateScore' is defined but never used.", "'BellAlertIcon' is defined but never used.", "'PaintBrushIcon' is defined but never used.", "'nextQuestion' is defined but never used.", "'handleNextQuestion' is assigned a value but never used.", "'handleShowAnswer' is assigned a value but never used.", "'handleStartTime' is assigned a value but never used.", "'handleStartRound' is assigned a value but never used.", "'handleCorrectAnswer' is assigned a value but never used.", "'hostInitialGrid' is assigned a value but never used.", "'initialGrid' is assigned a value but never used.", "'easyQuestionNumber' is assigned a value but never used.", "'mediumQuestionNumber' is assigned a value but never used.", "'hardQuestionNumber' is assigned a value but never used.", "'level' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'roomId' and 'setInGameQuestionIndex'. Either include them or remove the dependency array.", ["1672"], "'playerFlashes' is assigned a value but never used.", "'isAnswerListFirstMounted' is assigned a value but never used.", ["1673"], "React Hook useEffect has a missing dependency: 'setPlayerScores'. Either include it or remove the dependency array.", ["1674"], ["1675"], ["1676"], "'showCurrentAnswer' is assigned a value but never used.", "'currentQuestion' is assigned a value but never used.", "'GetQuestionsRequest' is defined but never used.", "'getPacketNames' is defined but never used.", "'questionNumber' is assigned a value but never used.", "'setHiddenTopics' is assigned a value but never used.", "'showReturnButton' is assigned a value but never used.", "'setCurrentQuestion' is defined but never used.", "'setRound4Grid' is defined but never used.", "'starPositions' is assigned a value but never used.", "'getQuestions' is assigned a value but never used.", "'currentRound' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'listenToGameState'. Either include it or remove the dependency array.", ["1677"], "'Round2Grid' is defined but never used.", "'setSelectedRow' is defined but never used.", "'setCorrectRow' is defined but never used.", "'setIncorectRow' is defined but never used.", "'openObstacle' is defined but never used.", "'generateGrid' is defined but never used.", "'PlayerAnswerInput' is defined but never used.", "'hintWords' is assigned a value but never used.", "'setHintWords' is assigned a value but never used.", "'isExpanded' is assigned a value but never used.", "'isModalOpen' is assigned a value but never used.", "'setIsModalOpen' is assigned a value but never used.", "'setCurrentPlayerName' is assigned a value but never used.", "'setCurrentPlayerAvatar' is assigned a value but never used.", "'setPosition' is assigned a value but never used.", "'answerList' is assigned a value but never used.", "'handleRowSelect' is assigned a value but never used.", "'handleRowCorrect' is assigned a value but never used.", "'handleRowIncorrect' is assigned a value but never used.", "'currentRoom' is assigned a value but never used.", ["1678"], ["1679"], "'addToast' is defined but never used.", ["1680"], "'setScores' is defined but never used.", "React Hook useCallback has a missing dependency: 'listener'. Either include it or remove the dependency array.", ["1681"], ["1682"], ["1683"], ["1684"], ["1685"], ["1686"], ["1687"], ["1688"], ["1689"], ["1690"], ["1691"], ["1692"], ["1693"], ["1694"], ["1695"], ["1696"], ["1697"], ["1698"], "'listenToReturnToCurrentTurn' is assigned a value but never used.", ["1699"], ["1700"], "React Hook useCallback has an unnecessary dependency: 'dispatch'. Either exclude it or remove the dependency array.", ["1701"], ["1702"], "'updateScores' is defined but never used.", "'setQuestions' is defined but never used.", "'setRound2Grid' is defined but never used.", "'clearError' is defined but never used.", "'SubmitAnswerRequest' is defined but never used.", "'ScoringRequest' is defined but never used.", "'SendGridRequest' is defined but never used.", "'dispatch' is assigned a value but never used.", "React Hook useCallback has missing dependencies: 'currentRound', 'round2Grid?.grid', and 'round4Grid?.grid'. Either include them or remove the dependency array.", ["1703"], "React Hook useCallback has a missing dependency: 'currentCorrectAnswer'. Either include it or remove the dependency array.", ["1704"], "'GetQuestionsResponse' is defined but never used.", "'ScoringResponse' is defined but never used.", "'off' is defined but never used.", "'push' is defined but never used.", "'DatabaseReference' is defined but never used.", "'LoginRequest' is defined but never used.", "'scores' is assigned a value but never used.", "'showRules' is assigned a value but never used.", "'updatePlayer' is assigned a value but never used.", "'setCurrentQuestionFirebase' is assigned a value but never used.", "'handleSubmitAnswer' is assigned a value but never used.", "'handleShowRules' is assigned a value but never used.", "'buzzing' is defined but never used.", "'setStar' is defined but never used.", "'closeBuzz' is defined but never used.", "'submitPlayerAnswer' is assigned a value but never used.", "'handleSetStar' is assigned a value but never used.", "'CreateRoomResponse' is defined but never used.", "'JoinRoomResponse' is defined but never used.", "'GetRoomsResponse' is defined but never used.", {"desc": "1705", "fix": "1706"}, {"desc": "1707", "fix": "1708"}, {"desc": "1709", "fix": "1710"}, {"desc": "1711", "fix": "1712"}, {"desc": "1713", "fix": "1714"}, {"desc": "1715", "fix": "1716"}, {"desc": "1715", "fix": "1717"}, {"desc": "1715", "fix": "1718"}, {"desc": "1715", "fix": "1719"}, {"desc": "1720", "fix": "1721"}, {"desc": "1715", "fix": "1722"}, {"desc": "1711", "fix": "1723"}, {"desc": "1724", "fix": "1725"}, {"desc": "1726", "fix": "1727"}, {"desc": "1728", "fix": "1729"}, {"desc": "1730", "fix": "1731"}, {"desc": "1732", "fix": "1733"}, {"desc": "1734", "fix": "1735"}, {"desc": "1713", "fix": "1736"}, {"desc": "1713", "fix": "1737"}, {"desc": "1738", "fix": "1739"}, {"desc": "1740", "fix": "1741"}, {"desc": "1742", "fix": "1743"}, {"desc": "1744", "fix": "1745"}, {"desc": "1746", "fix": "1747"}, {"desc": "1746", "fix": "1748"}, {"desc": "1713", "fix": "1749"}, {"desc": "1750", "fix": "1751"}, {"desc": "1752", "fix": "1753"}, {"desc": "1734", "fix": "1754"}, {"desc": "1755", "fix": "1756"}, {"desc": "1757", "fix": "1758"}, {"desc": "1750", "fix": "1759"}, {"desc": "1760", "fix": "1761"}, {"desc": "1762", "fix": "1763"}, {"desc": "1726", "fix": "1764"}, {"desc": "1734", "fix": "1765"}, {"desc": "1732", "fix": "1766"}, {"desc": "1767", "fix": "1768"}, {"desc": "1713", "fix": "1769"}, {"desc": "1713", "fix": "1770"}, {"desc": "1755", "fix": "1771"}, {"desc": "1734", "fix": "1772"}, {"desc": "1726", "fix": "1773"}, {"desc": "1734", "fix": "1774"}, {"desc": "1732", "fix": "1775"}, {"desc": "1767", "fix": "1776"}, {"desc": "1713", "fix": "1777"}, {"desc": "1713", "fix": "1778"}, {"desc": "1779", "fix": "1780"}, {"desc": "1755", "fix": "1781"}, {"desc": "1782", "fix": "1783"}, {"desc": "1734", "fix": "1784"}, {"desc": "1785", "fix": "1786"}, {"desc": "1787", "fix": "1788"}, {"desc": "1713", "fix": "1789"}, {"desc": "1790", "fix": "1791"}, {"desc": "1792", "fix": "1793"}, {"desc": "1794", "fix": "1795"}, {"desc": "1796", "fix": "1797"}, {"desc": "1798", "fix": "1799"}, {"desc": "1800", "fix": "1801"}, {"desc": "1802", "fix": "1803"}, {"desc": "1804", "fix": "1805"}, {"desc": "1806", "fix": "1807"}, {"desc": "1808", "fix": "1809"}, {"desc": "1810", "fix": "1811"}, {"desc": "1812", "fix": "1813"}, {"desc": "1814", "fix": "1815"}, {"desc": "1816", "fix": "1817"}, {"desc": "1818", "fix": "1819"}, {"desc": "1820", "fix": "1821"}, {"desc": "1822", "fix": "1823"}, {"desc": "1812", "fix": "1824"}, {"desc": "1825", "fix": "1826"}, {"desc": "1825", "fix": "1827"}, {"desc": "1825", "fix": "1828"}, {"desc": "1825", "fix": "1829"}, {"desc": "1830", "fix": "1831"}, {"desc": "1832", "fix": "1833"}, {"desc": "1834", "fix": "1835"}, {"desc": "1834", "fix": "1836"}, {"desc": "1834", "fix": "1837"}, {"desc": "1834", "fix": "1838"}, {"desc": "1834", "fix": "1839"}, {"desc": "1834", "fix": "1840"}, {"desc": "1834", "fix": "1841"}, {"desc": "1834", "fix": "1842"}, {"desc": "1832", "fix": "1843"}, {"desc": "1832", "fix": "1844"}, {"desc": "1832", "fix": "1845"}, {"desc": "1832", "fix": "1846"}, {"desc": "1834", "fix": "1847"}, {"desc": "1834", "fix": "1848"}, {"desc": "1834", "fix": "1849"}, {"desc": "1834", "fix": "1850"}, {"desc": "1832", "fix": "1851"}, {"desc": "1834", "fix": "1852"}, {"desc": "1750", "fix": "1853"}, {"desc": "1854", "fix": "1855"}, {"desc": "1856", "fix": "1857"}, {"desc": "1858", "fix": "1859"}, "Update the dependencies array to be: [hostRoomId]", {"range": "1860", "text": "1861"}, "Update the dependencies array to be: [handleTimeEnd, timeLeft]", {"range": "1862", "text": "1863"}, "Update the dependencies array to be: [location.pathname, requireAccessToken, requireHost, roomId]", {"range": "1864", "text": "1865"}, "Update the dependencies array to be: [roomId, testName]", {"range": "1866", "text": "1867"}, "Update the dependencies array to be: [roomId, sounds]", {"range": "1868", "text": "1869"}, "Update the dependencies array to be: [isSpectator, navigate, roomId, round, setInitialGrid]", {"range": "1870", "text": "1871"}, {"range": "1872", "text": "1871"}, {"range": "1873", "text": "1871"}, {"range": "1874", "text": "1871"}, "Update the dependencies array to be: [images.length]", {"range": "1875", "text": "1876"}, {"range": "1877", "text": "1871"}, {"range": "1878", "text": "1867"}, "Update the dependencies array to be: [authenticateUser]", {"range": "1879", "text": "1880"}, "Update the dependencies array to be: [playerAnswerRef]", {"range": "1881", "text": "1882"}, "Update the dependencies array to be: [listenToTimeStart, startTimer]", {"range": "1883", "text": "1884"}, "Update the dependencies array to be: [deletePath, listenToSound]", {"range": "1885", "text": "1886"}, "Update the dependencies array to be: [currentPlayerAvatar, currentPlayerName, isHost, isSpectator, playerAnswerRef, playerAnswerTime, position, roomId, setAnimationKey, timeLeft]", {"range": "1887", "text": "1888"}, "Update the dependencies array to be: [roomId, startTimer]", {"range": "1889", "text": "1890"}, {"range": "1891", "text": "1869"}, {"range": "1892", "text": "1869"}, "Update the dependencies array to be: [hintWordArray, obstacleWord, initialGrid, isHost, setInitialGrid]", {"range": "1893", "text": "1894"}, "Update the dependencies array to be: [roomId, grid, revealCellsForPlayer]", {"range": "1895", "text": "1896"}, "Update the dependencies array to be: [roomId, setAnswerList]", {"range": "1897", "text": "1898"}, "Update the dependencies array to be: [roomId, grid, setAnswerList, revealCellsForPlayer]", {"range": "1899", "text": "1900"}, "Update the dependencies array to be: [roomId, grid, sounds, revealCellsForPlayer]", {"range": "1901", "text": "1902"}, {"range": "1903", "text": "1902"}, {"range": "1904", "text": "1869"}, "Update the dependencies array to be: [roomId]", {"range": "1905", "text": "1906"}, "Update the dependencies array to be: [roomId, isHost, setCurrentQuestion, setSelectedTopic]", {"range": "1907", "text": "1908"}, {"range": "1909", "text": "1890"}, "Update the dependencies array to be: [setAnimationKey, timeLeft]", {"range": "1910", "text": "1911"}, "Update the dependencies array to be: [isHost, roomId, testName]", {"range": "1912", "text": "1913"}, {"range": "1914", "text": "1906"}, "Update the dependencies array to be: [isHost, roomId, setSelectedTopic]", {"range": "1915", "text": "1916"}, "Update the dependencies array to be: [currentAnswer, isHost, roomId, setAnswerList, setCurrentQuestion]", {"range": "1917", "text": "1918"}, {"range": "1919", "text": "1882"}, {"range": "1920", "text": "1890"}, {"range": "1921", "text": "1888"}, "Update the dependencies array to be: [currentAnswer, isHost, roomId, setAnswerList]", {"range": "1922", "text": "1923"}, {"range": "1924", "text": "1869"}, {"range": "1925", "text": "1869"}, {"range": "1926", "text": "1911"}, {"range": "1927", "text": "1890"}, {"range": "1928", "text": "1882"}, {"range": "1929", "text": "1890"}, {"range": "1930", "text": "1888"}, {"range": "1931", "text": "1923"}, {"range": "1932", "text": "1869"}, {"range": "1933", "text": "1869"}, "Update the dependencies array to be: [initialGrid, setInitialGrid]", {"range": "1934", "text": "1935"}, {"range": "1936", "text": "1911"}, "Update the dependencies array to be: [roomId, setPlayerFlashes, setScoreList, round, listenToScores]", {"range": "1937", "text": "1938"}, {"range": "1939", "text": "1890"}, "Update the dependencies array to be: [roomId, setBuzzedPlayer, setShowModal, sounds]", {"range": "1940", "text": "1941"}, "Update the dependencies array to be: [roomId, setShowModal, setStaredPlayer, sounds]", {"range": "1942", "text": "1943"}, {"range": "1944", "text": "1869"}, "Update the dependencies array to be: [roomId, setCorrectAnswer, sounds]", {"range": "1945", "text": "1946"}, "Update the dependencies array to be: [roomId, setCorrectAnswer, setCurrentQuestion]", {"range": "1947", "text": "1948"}, "Update the dependencies array to be: [roomId, setGridColors, setSelectedCell]", {"range": "1949", "text": "1950"}, "Update the dependencies array to be: [colorMap, roomId, setGrid, setGridColors]", {"range": "1951", "text": "1952"}, "Update the dependencies array to be: [currentQuestion, dispatch]", {"range": "1953", "text": "1954"}, "Update the dependencies array to be: [listenToNewPlayer]", {"range": "1955", "text": "1956"}, "Update the dependencies array to be: [dispatch, isHost, listenToCurrentQuestion]", {"range": "1957", "text": "1958"}, "Update the dependencies array to be: [listenToCorrectAnswer, sounds]", {"range": "1959", "text": "1960"}, "Update the dependencies array to be: [roomId, setupDisconnect, userId]", {"range": "1961", "text": "1962"}, "Update the dependencies array to be: [listenToSpectatorJoin]", {"range": "1963", "text": "1964"}, "Update the dependencies array to be: [listenToCurrentTurn, roomId]", {"range": "1965", "text": "1966"}, "Update the dependencies array to be: [listenToBroadcastedAnswer, roomId]", {"range": "1967", "text": "1968"}, "Update the dependencies array to be: [listenToOpenBuzz, roomId, round, sounds]", {"range": "1969", "text": "1970"}, "Update the dependencies array to be: [currentRound, roomId, setInGameQuestionIndex]", {"range": "1971", "text": "1972"}, "Update the dependencies array to be: [roomId, round]", {"range": "1973", "text": "1974"}, "Update the dependencies array to be: [round, setPlayerScores]", {"range": "1975", "text": "1976"}, "Update the dependencies array to be: [round, roomId, listenToScores]", {"range": "1977", "text": "1978"}, {"range": "1979", "text": "1968"}, "Update the dependencies array to be: [roomId, round, isSpectator, navigate, dispatch, listenToGameState]", {"range": "1980", "text": "1981"}, {"range": "1982", "text": "1981"}, {"range": "1983", "text": "1981"}, {"range": "1984", "text": "1981"}, "Update the dependencies array to be: [roomId, dispatch, listener]", {"range": "1985", "text": "1986"}, "Update the dependencies array to be: [roomId, listener]", {"range": "1987", "text": "1988"}, "Update the dependencies array to be: [roomId, listener, dispatch]", {"range": "1989", "text": "1990"}, {"range": "1991", "text": "1990"}, {"range": "1992", "text": "1990"}, {"range": "1993", "text": "1990"}, {"range": "1994", "text": "1990"}, {"range": "1995", "text": "1990"}, {"range": "1996", "text": "1990"}, {"range": "1997", "text": "1990"}, {"range": "1998", "text": "1988"}, {"range": "1999", "text": "1988"}, {"range": "2000", "text": "1988"}, {"range": "2001", "text": "1988"}, {"range": "2002", "text": "1990"}, {"range": "2003", "text": "1990"}, {"range": "2004", "text": "1990"}, {"range": "2005", "text": "1990"}, {"range": "2006", "text": "1988"}, {"range": "2007", "text": "1990"}, {"range": "2008", "text": "1906"}, "Update the dependencies array to be: [listener, roomId]", {"range": "2009", "text": "2010"}, "Update the dependencies array to be: [currentRound, round2Grid?.grid, round4Grid?.grid]", {"range": "2011", "text": "2012"}, "Update the dependencies array to be: [currentCorrectAnswer]", {"range": "2013", "text": "2014"}, [3417, 3419], "[hostRoomId]", [2159, 2169], "[handleTimeEnd, timeLeft]", [5280, 5332], "[location.pathname, requireAccessToken, requireHost, roomId]", [1936, 1938], "[roomId, testName]", [995, 997], "[roomId, sounds]", [2208, 2216], "[isSpectator, navigate, roomId, round, setInitialGrid]", [2436, 2444], [2276, 2284], [3055, 3063], [519, 521], "[images.length]", [2257, 2265], [1146, 1148], [1412, 1414], "[authenticateUser]", [2056, 2081], "[playerAnswerRef]", [2317, 2319], "[listenToTimeStart, startTimer]", [2559, 2561], "[deletePath, listenToSound]", [4341, 4351], "[currentP<PERSON><PERSON><PERSON><PERSON>, currentPlayer<PERSON><PERSON>, isHost, isSpectator, playerAnswerRef, playerAnswerTime, position, roomId, setAnimationKey, timeLeft]", [4802, 4804], "[roomId, startTimer]", [7254, 7262], [7684, 7686], [10089, 10131], "[hint<PERSON>ordArray, obstacle<PERSON>ord, initialGrid, isHost, setInitialGrid]", [22416, 22430], "[roomId, grid, revealCellsForPlayer]", [22788, 22790], "[roomId, setAnswerList]", [23836, 23850], "[roomId, grid, setAnswerList, revealCellsForPlayer]", [25089, 25103], "[roomId, grid, sounds, revealCellsForPlayer]", [26651, 26665], [6173, 6175], [6699, 6701], "[roomId]", [7869, 7885], "[roomId, isHost, setCurrentQuestion, setSelectedTopic]", [8798, 8800], [9659, 9669], "[setAnimationKey, timeLeft]", [9905, 9907], "[isHost, roomId, testName]", [10225, 10227], [10814, 10816], "[isHost, roomId, setSelectedTopic]", [12101, 12103], "[current<PERSON><PERSON><PERSON>, isHost, roomId, setAnswerList, setCurrentQuestion]", [1778, 1803], [2486, 2488], [3335, 3345], [4062, 4064], "[current<PERSON>ns<PERSON>, isHost, roomId, setAnswerList]", [4550, 4552], [4997, 4999], [3367, 3377], [3869, 3871], [1768, 1793], [2476, 2478], [3325, 3335], [4052, 4054], [4540, 4542], [4987, 4989], [12138, 12140], "[initialGrid, setInitialGrid]", [12757, 12767], [884, 931], "[roomId, setPlayerFlashes, setScoreList, round, listenToScores]", [1852, 1854], [2759, 2767], "[roomId, setBuzzedPlayer, setShowModal, sounds]", [3683, 3691], "[roomId, setShowModal, setStaredPlayer, sounds]", [4177, 4179], [4810, 4812], "[roomId, setCorrectAnswer, sounds]", [5136, 5138], "[roomId, setCorrectAnswer, setCurrentQuestion]", [7006, 7008], "[roomId, setGridColors, setSelectedCell]", [7959, 7961], "[colorMap, roomId, setGrid, setGridColors]", [1846, 1863], "[currentQuestion, dispatch]", [4841, 4843], "[listenToNewPlayer]", [5176, 5178], "[dispatch, isHost, listenToCurrentQuestion]", [5525, 5527], "[listenToCorrectAnswer, sounds]", [7072, 7088], "[roomId, setupDisconnect, userId]", [7259, 7261], "[listenToSpectatorJoin]", [1598, 1606], "[listenT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, roomId]", [2171, 2179], "[listenToBroadcastedAnswer, roomId]", [2946, 2961], "[listenToOpenBuzz, roomId, round, sounds]", [6410, 6424], "[currentRound, roomId, setInGameQuestionIndex]", [2661, 2668], "[roomId, round]", [2879, 2886], "[round, setPlayerScores]", [3018, 3033], "[round, roomId, listenToScores]", [3198, 3206], [3292, 3340], "[roomId, round, isSpectator, navigate, dispatch, listenToGameState]", [4521, 4569], [2968, 3016], [3669, 3717], [1669, 1687], "[roomId, dispatch, listener]", [1861, 1879], "[roomId, listener]", [2162, 2180], "[roomId, listener, dispatch]", [2481, 2499], [2786, 2804], [3111, 3129], [3411, 3429], [3706, 3724], [4722, 4740], [5080, 5098], [5389, 5407], [5699, 5717], [6014, 6032], [6322, 6340], [6687, 6705], [7104, 7122], [7487, 7505], [7916, 7934], [8252, 8270], [8600, 8618], [9246, 9264], [12482, 12490], "[listener, roomId]", [4249, 4251], "[currentRound, round2Grid?.grid, round4Grid?.grid]", [6479, 6481], "[currentCorrectAnswer]"]