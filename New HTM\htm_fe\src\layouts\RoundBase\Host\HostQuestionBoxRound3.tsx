import React, { useEffect, useState } from 'react'
import { useFirebaseListener } from '../../../shared/hooks';
import { useAppDispatch, useAppSelector } from '../../../app/store';
import { useSearchParams } from 'react-router-dom';
import { getPacketsName, setCurrentCorrectAnswer, setCurrentQuestion, setCurrentQuestionNumber, setSelectedPacketName, setUsedPackesName } from '../../../app/store/slices/gameSlice';
import { gameApi } from '../../../shared/services';
const HostQuestionBoxRound3: React.FC = () => {
    const [searchParams] = useSearchParams()
    const roomId = searchParams.get("roomId") || ""

    //local state
    const [showReturnButton, setShowReturnButton] = useState(false);
    
    // listener
    const { listenToUsedPackets } = useFirebaseListener(roomId)

    //global state
    const { shouldReturnToTopicSelection, selectedPacketName, usedPacketNames, packetNames } = useAppSelector((state) => state.game);
    const dispatch = useAppDispatch()

    //api
    const {sendSelectedPacketName, sendUsedPacketName} = gameApi

    useEffect(()=> {
        async () => {
            await dispatch(getPacketsName())
        }
    },[])
    // Listen to used topics
    useEffect(() => {
        const unsubscribeUsedTopics = listenToUsedPackets()


        return () => {
            unsubscribeUsedTopics();
        };
    }, [roomId]);

    const handleTopicSelect = async (topic: string) => {
        console.log("topic", topic);
        dispatch(setCurrentQuestionNumber(0))
        dispatch(setCurrentQuestion(null))

        dispatch(setCurrentCorrectAnswer(""))
        dispatch(setUsedPackesName([...usedPacketNames, topic]))
        dispatch(setSelectedPacketName(topic))

        setShowReturnButton(true)

        await sendSelectedPacketName(roomId, topic)
        await sendUsedPacketName(roomId, [...usedPacketNames, topic])
    };

    const handleReturnToTopicSelection = async () => {
        // if (!isHost) return;

        // Mark current topic as used
        // if (selectedTopic) {
        //     setUsedTopic(roomId, selectedTopic);
        // }

        // // Clear current game state
        // setCurrentQuestion("");
        // setCorrectAnswer("");
        dispatch(setSelectedPacketName(null))
        setShowReturnButton(false);
        //setPlayerCurrentQuestionIndex(-1);
        //setCurrentQuestionIndex("-1")

        // Clear Firebase paths
        deletePath(roomId, "currentQuestions");
        deletePath(roomId, "current_correct_answer");

        // Notify all players to return to topic selection
        setReturnToTopicSelection(roomId, true);

        console.log("Returned to topic selection");
    };

    const handleToggleUsedTopic = (topic: string) => {
        if (!isHost) return;

        if (usedTopics.includes(topic)) {
            // Remove from used topics
            const updatedTopics = usedTopics.filter(t => t !== topic);
            // Update Firebase with new list
            const usedTopicsRef = ref(database, `rooms/${roomId}/usedTopics`);
            set(usedTopicsRef, updatedTopics);
        } else {
            // Add to used topics
            setUsedTopic(roomId, topic);
        }
    };
    return (
        <div className="flex flex-col items-center min-h-[600px]">
            {!selectedPacketName ? (
                <div className="grid grid-cols-2 gap-6 w-full max-w-xl">
                    {Array.isArray(packetNames) && packetNames.length > 0 ? (
                        packetNames
                            .slice(0, 8) // Only show up to 8 packages
                            .map((packet) => (
                                <div key={packet} className="relative">
                                    <button
                                        className={`w-full bg-blue-500 text-white text-xl font-bold p-8 rounded-2xl shadow-lg hover:bg-blue-700 transition-all duration-200 ${!isHost ? "cursor-not-allowed opacity-50" : ""
                                            } ${usedPacketNames.includes(packet) ? "opacity-60 bg-gray-500" : ""}`}
                                        onClick={() => handleTopicSelect(topic)}
                                        style={{ minHeight: "100px" }}
                                    >
                                        {packet}
                                    </button>
                                    {isHost && (
                                        <div className="absolute top-2 right-2 flex items-center">
                                            <input
                                                type="checkbox"
                                                checked={usedPacketNames.includes(packet)}
                                                onChange={() => handleToggleUsedTopic(topic)}
                                                className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 cursor-pointer"
                                            />
                                            <label className="ml-1 text-xs text-white font-semibold">
                                                {usedPacketNames.includes(packet) ? "✓" : ""}
                                            </label>
                                        </div>
                                    )}
                                </div>
                            ))
                    ) : null}
                </div>
            ) : (
                <div className="w-full text-center">
                    <h2 className="text-xl font-bold text-white">{selectedPacketName ? selectedPacketName : ""}</h2>
                    <div className="my-4">
                        <p className="text-lg mt-2 text-white">
                            {currentQuestion ? currentQuestion : ""}
                        </p>
                        <p className="text-lg mt-2 text-white">
                            {correctAnswer ? correctAnswer : ""}
                        </p>




                        {/* Return to topic selection button */}
                        {showReturnButton && isHost && (
                            <>
                                <div className="mt-4">

                                    <div className="flex flex-wrap gap-2 justify-center">
                                        <button
                                            onClick={() => {
                                                updateScore(roomId, [], "auto", "3", selectedPlayer?.stt.toString(), "true");
                                                setInGameQuestionIndex((prev: number) => prev + 1);
                                                handleNextQuestion(selectedTopic);
                                            }}
                                            className="bg-green-600 hover:bg-green-700 text-white min-w-[120px] rounded py-2 px-4"
                                        >
                                            Đúng
                                        </button>
                                        <button
                                            onClick={() => {
                                                setInGameQuestionIndex((prev: number) => prev + 1);
                                                handleNextQuestion(selectedTopic);
                                            }}
                                            className="bg-red-600 hover:bg-red-700 text-white min-w-[120px] rounded py-2 px-4"
                                        >
                                            Sai
                                        </button>
                                    </div>
                                </div>
                                <div className="mt-6 text-center">
                                    <button
                                        onClick={handleReturnToTopicSelection}
                                        className="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-6 rounded-lg shadow-lg transition-all duration-200"
                                    >
                                        Quay về màn hình chọn gói
                                    </button>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
}

export default HostQuestionBoxRound3