{"ast": null, "code": "// Game Redux slice\nimport { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport apiClient from '../../../shared/services/api/client';\nimport gameApi from '../../../shared/services/game/gameApi';\n\n// Initial state\nconst initialState = {\n  // Current game status\n  currentRound: \"1\",\n  currentTestName: \"\",\n  isActive: false,\n  isHost: false,\n  // Questions and answers\n  currentQuestion: null,\n  selectedPacketName: \"\",\n  packetNames: [],\n  usedPacketNames: [],\n  questions: [],\n  currentCorrectAnswer: \"\",\n  // Players and scoring\n  players: [],\n  currentPlayer: null,\n  scores: [],\n  scoreRules: null,\n  // Round-specific data\n  round2Grid: null,\n  round4Grid: null,\n  buzzedPlayerName: \"\",\n  // Game settings\n  mode: 'manual',\n  timeLimit: 30,\n  // UI state\n  showRules: false,\n  currentTurn: 0,\n  currentQuestionNumber: 1,\n  isBuzzOpen: false,\n  // Loading states\n  loading: {\n    isLoading: false,\n    error: null\n  },\n  // joing states\n  joining: {\n    isLoading: false,\n    error: null\n  },\n  //input disabled\n  isInputDisabled: true\n};\nexport const joinRoom = createAsyncThunk('room/joinRoom', async (joinData, {\n  rejectWithValue\n}) => {\n  try {\n    const url = new URL('/api/room/join', process.env.REACT_APP_BASE_URL);\n    url.searchParams.append('room_id', joinData.roomId);\n    if (joinData.password) {\n      url.searchParams.append('password', joinData.password);\n    }\n    const response = await apiClient.post(url.toString(), joinData, {\n      _isAuthRequired: true\n    });\n    return response.data;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\n// Async thunks\nexport const getQuestions = createAsyncThunk('game/fetchQuestions', async ({\n  questionNumber,\n  isJump = false,\n  round\n}, {\n  rejectWithValue,\n  getState\n}) => {\n  try {\n    const state = getState();\n    const {\n      currentTestName,\n      currentQuestionNumber,\n      currentRound,\n      selectedPacketName\n    } = state.game;\n    const targetQuestionNumber = isJump && questionNumber !== undefined ? questionNumber : currentQuestionNumber + 1;\n    const nextQuestion = {\n      testName: currentTestName,\n      questionNumber: round !== \"2\" ? targetQuestionNumber : currentQuestionNumber,\n      round: currentRound,\n      packetName: currentRound === \"3\" ? selectedPacketName : undefined\n    };\n    const question = await gameApi.getQuestions(nextQuestion);\n    return {\n      question,\n      targetQuestionNumber\n    };\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\nexport const submitAnswer = createAsyncThunk('game/submitAnswer', async (params, {\n  rejectWithValue\n}) => {\n  try {\n    // TODO: Replace with actual API call\n    const response = await fetch('/api/game/answer', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(params)\n    });\n    if (!response.ok) {\n      throw new Error('Failed to submit answer');\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\nexport const updateScores = createAsyncThunk('game/updateScores', async (params, {\n  rejectWithValue\n}) => {\n  try {\n    // TODO: Replace with actual API call\n    const response = await fetch('/api/game/scoring', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(params)\n    });\n    if (!response.ok) {\n      throw new Error('Failed to update scores');\n    }\n    const data = await response.json();\n    return data.scores;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\n\n// Game slice\nconst gameSlice = createSlice({\n  name: 'game',\n  initialState,\n  reducers: {\n    // Game state management\n    setCurrentRound: (state, action) => {\n      state.currentRound = action.payload;\n      state.currentQuestionNumber = 1; // Reset question number when round changes\n    },\n    setCurrentTestName: (state, action) => {\n      state.currentTestName = action.payload;\n    },\n    setSelectedPacketName: (state, action) => {\n      state.selectedPacketName = action.payload;\n    },\n    setIsInputDisabled: (state, action) => {\n      state.isInputDisabled = action.payload;\n    },\n    setIsActive: (state, action) => {\n      state.isActive = action.payload;\n    },\n    setIsHost: (state, action) => {\n      state.isHost = action.payload;\n    },\n    // Question management\n    setCurrentQuestion: (state, action) => {\n      state.currentQuestion = action.payload;\n    },\n    setQuestions: (state, action) => {\n      state.questions = action.payload;\n    },\n    setCurrentCorrectAnswer: (state, action) => {\n      state.currentCorrectAnswer = action.payload;\n    },\n    setPlayerAnswerList: (state, action) => {\n      state.players = state.players.map(player => {\n        const answerUpdate = action.payload.find(a => a.uid === player.uid);\n        return answerUpdate ? {\n          ...player,\n          ...answerUpdate\n        } : player;\n      });\n    },\n    clearPlayerAnswerList: state => {\n      state.players = state.players.map(player => ({\n        ...player,\n        answer: \"\",\n        time: 0\n      }));\n    },\n    nextQuestion: state => {\n      state.currentQuestionNumber += 1;\n    },\n    setCurrentQuestionNumber: (state, action) => {\n      state.currentQuestionNumber = action.payload;\n    },\n    // Player management\n    setPlayers: (state, action) => {\n      state.players = state.players.map(player => {\n        const update = action.payload.find(p => p && p.uid === player.uid);\n        return update ? {\n          ...player,\n          ...update\n        } : player;\n      });\n    },\n    setCurrentPlayer: (state, action) => {\n      state.currentPlayer = action.payload;\n    },\n    updatePlayer: (state, action) => {\n      const playerIndex = state.players.findIndex(p => p.uid === action.payload.uid);\n      if (playerIndex !== -1) {\n        state.players[playerIndex] = {\n          ...state.players[playerIndex],\n          ...action.payload.updates\n        };\n      }\n    },\n    addPlayer: (state, action) => {\n      const existingIndex = state.players.findIndex(p => p.uid === action.payload.uid);\n      if (existingIndex === -1) {\n        state.players.push(action.payload);\n      }\n    },\n    setBuzzedPlayer: (state, action) => {\n      state.buzzedPlayerName = action.payload;\n    },\n    removePlayer: (state, action) => {\n      state.players = state.players.filter(p => p.uid !== action.payload);\n    },\n    setPlayerAnswer: (state, action) => {\n      if (state.currentPlayer) {\n        state.currentPlayer.answer = action.payload.answer;\n        state.currentPlayer.time = action.payload.time;\n      }\n    },\n    // Scoring\n    setScores: (state, action) => {\n      state.scores = action.payload;\n    },\n    setScoreRules: (state, action) => {\n      state.scoreRules = action.payload;\n    },\n    // Round-specific data\n    setRound2Grid: (state, action) => {\n      if (action.payload === null) {\n        state.round2Grid = null;\n      } else {\n        state.round2Grid = {\n          ...(state.round2Grid || {}),\n          ...action.payload\n        };\n      }\n    },\n    setRound4Grid: (state, action) => {\n      if (action.payload === null) {\n        state.round4Grid = null;\n      } else {\n        state.round4Grid = {\n          ...(state.round4Grid || {}),\n          ...action.payload\n        };\n      }\n    },\n    // Game settings\n    setMode: (state, action) => {\n      state.mode = action.payload;\n    },\n    setTimeLimit: (state, action) => {\n      state.timeLimit = action.payload;\n    },\n    // UI state\n    setShowRules: (state, action) => {\n      state.showRules = action.payload;\n    },\n    setCurrentTurn: (state, action) => {\n      state.currentTurn = action.payload;\n    },\n    setIsBuzzOpen: (state, action) => {\n      state.isBuzzOpen = action.payload;\n    },\n    // Reset game state\n    resetGame: state => {\n      return {\n        ...initialState,\n        isHost: state.isHost\n      };\n    },\n    // Error handling\n    clearError: state => {\n      state.loading.error = null;\n    }\n  },\n  extraReducers: builder => {\n    // Join room\n    builder.addCase(joinRoom.pending, state => {\n      state.joining.isLoading = true;\n      state.joining.error = null;\n    }).addCase(joinRoom.fulfilled, (state, action) => {\n      state.joining.isLoading = false;\n      state.players = action.payload.players;\n      state.currentPlayer = {\n        ...state.currentPlayer,\n        ...action.meta.arg,\n        uid: action.payload.uid\n      };\n      state.isHost = false;\n    }).addCase(joinRoom.rejected, (state, action) => {\n      state.joining.isLoading = false;\n      state.joining.error = action.payload;\n    });\n    // Fetch questions\n    builder.addCase(getQuestions.pending, state => {\n      state.loading.isLoading = true;\n      state.loading.error = null;\n    }).addCase(getQuestions.fulfilled, (state, action) => {\n      state.loading.isLoading = false;\n      state.currentQuestion = action.payload.question;\n      state.currentCorrectAnswer = action.payload.question.answer;\n      state.currentQuestionNumber = action.payload.targetQuestionNumber;\n    }).addCase(getQuestions.rejected, (state, action) => {\n      state.loading.isLoading = false;\n      state.loading.error = action.payload;\n    });\n\n    // Submit answer\n    builder.addCase(submitAnswer.pending, state => {\n      state.loading.isLoading = true;\n    }).addCase(submitAnswer.fulfilled, (state, action) => {\n      state.loading.isLoading = false;\n      // Handle answer submission result\n    }).addCase(submitAnswer.rejected, (state, action) => {\n      state.loading.isLoading = false;\n      state.loading.error = action.payload;\n    });\n\n    // Update scores\n    builder.addCase(updateScores.fulfilled, (state, action) => {\n      state.scores = action.payload;\n    }).addCase(updateScores.rejected, (state, action) => {\n      state.loading.error = action.payload;\n    });\n  }\n});\nexport const {\n  setCurrentRound,\n  setIsActive,\n  setIsHost,\n  setCurrentQuestion,\n  setQuestions,\n  setCurrentCorrectAnswer,\n  nextQuestion,\n  setCurrentQuestionNumber,\n  setPlayers,\n  setCurrentPlayer,\n  setPlayerAnswer,\n  setPlayerAnswerList,\n  clearPlayerAnswerList,\n  updatePlayer,\n  addPlayer,\n  removePlayer,\n  setScores,\n  setScoreRules,\n  setRound2Grid,\n  setBuzzedPlayer,\n  setRound4Grid,\n  setMode,\n  setTimeLimit,\n  setShowRules,\n  setCurrentTurn,\n  resetGame,\n  clearError,\n  setIsInputDisabled,\n  setIsBuzzOpen\n} = gameSlice.actions;\nexport default gameSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "apiClient", "gameApi", "initialState", "currentRound", "currentTestName", "isActive", "isHost", "currentQuestion", "selectedPacketName", "packetNames", "usedPacketNames", "questions", "currentCorrectAnswer", "players", "currentPlayer", "scores", "scoreRules", "round2Grid", "round4Grid", "buzzedPlayerName", "mode", "timeLimit", "showRules", "currentTurn", "currentQuestionNumber", "isBuzzOpen", "loading", "isLoading", "error", "joining", "isInputDisabled", "joinRoom", "joinData", "rejectWithValue", "url", "URL", "process", "env", "REACT_APP_BASE_URL", "searchParams", "append", "roomId", "password", "response", "post", "toString", "_isAuthRequired", "data", "message", "getQuestions", "questionNumber", "isJump", "round", "getState", "state", "game", "targetQuestionNumber", "undefined", "nextQuestion", "testName", "packetName", "question", "submitAnswer", "params", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "json", "updateScores", "gameSlice", "name", "reducers", "setCurrentRound", "action", "payload", "setCurrentTestName", "setSelectedPacketName", "setIsInputDisabled", "setIsActive", "setIsHost", "setCurrentQuestion", "setQuestions", "setCurrentCorrectAnswer", "setPlayerAnswerList", "map", "player", "answerUpdate", "find", "a", "uid", "clearPlayerAnswerList", "answer", "time", "setCurrentQuestionNumber", "setPlayers", "update", "p", "setCurrentPlayer", "updatePlayer", "playerIndex", "findIndex", "updates", "addPlayer", "existingIndex", "push", "setBuzzedPlayer", "removePlayer", "filter", "setPlayerAnswer", "setScores", "setScoreRules", "setRound2Grid", "setRound4Grid", "setMode", "setTimeLimit", "setShowRules", "setCurrentTurn", "setIsBuzzOpen", "resetGame", "clearError", "extraReducers", "builder", "addCase", "pending", "fulfilled", "meta", "arg", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/app/store/slices/gameSlice.ts"], "sourcesContent": ["// Game Redux slice\r\nimport { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\r\nimport { GameState, Question, Score, PlayerData, Round2Grid, Round4Grid, ScoreRule, RoomPlayer, JoinRoomRequest, Answer, GetQuestionsRequest } from '../../../shared/types';\r\nimport apiClient from '../../../shared/services/api/client';\r\nimport { set } from 'firebase/database';\r\nimport gameApi from '../../../shared/services/game/gameApi';\r\n\r\n// Initial state\r\nconst initialState: GameState = {\r\n  // Current game status\r\n  currentRound: \"1\",\r\n  currentTestName: \"\",\r\n  isActive: false,\r\n  isHost: false,\r\n\r\n  // Questions and answers\r\n  currentQuestion: null,\r\n  selectedPacketName: \"\",\r\n  packetNames: [],\r\n  usedPacketNames: [],\r\n  questions: [],\r\n  currentCorrectAnswer: \"\",\r\n  // Players and scoring\r\n  players: [],\r\n  currentPlayer: null,\r\n  scores: [],\r\n  scoreRules: null,\r\n\r\n  // Round-specific data\r\n  round2Grid: null,\r\n  round4Grid: null,\r\n  buzzedPlayerName: \"\",\r\n\r\n  // Game settings\r\n  mode: 'manual',\r\n  timeLimit: 30,\r\n\r\n  // UI state\r\n  showRules: false,\r\n  currentTurn: 0,\r\n  currentQuestionNumber: 1,\r\n  isBuzzOpen: false,\r\n\r\n  // Loading states\r\n  loading: {\r\n    isLoading: false,\r\n    error: null,\r\n  },\r\n\r\n  // joing states\r\n  joining: {\r\n    isLoading: false,\r\n    error: null,\r\n  },\r\n\r\n  //input disabled\r\n  isInputDisabled: true,\r\n};\r\n\r\n\r\nexport const joinRoom = createAsyncThunk(\r\n  'room/joinRoom',\r\n  async (joinData: JoinRoomRequest, { rejectWithValue }) => {\r\n    try {\r\n      const url = new URL('/api/room/join', process.env.REACT_APP_BASE_URL);\r\n      url.searchParams.append('room_id', joinData.roomId);\r\n      if (joinData.password) {\r\n        url.searchParams.append('password', joinData.password);\r\n      }\r\n      const response = await apiClient.post(url.toString(), joinData, { _isAuthRequired: true } as any);\r\n\r\n      return response.data;\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n// Async thunks\r\nexport const getQuestions = createAsyncThunk(\r\n  'game/fetchQuestions',\r\n  async ({ questionNumber, isJump = false, round }: { questionNumber?: number; isJump?: boolean; round?: string }, { rejectWithValue, getState }) => {\r\n    try {\r\n      const state = getState() as { game: GameState };\r\n\r\n      const { currentTestName, currentQuestionNumber, currentRound, selectedPacketName } = state.game;\r\n\r\n      const targetQuestionNumber = isJump && questionNumber !== undefined\r\n        ? questionNumber\r\n        : currentQuestionNumber + 1;\r\n\r\n\r\n      const nextQuestion = {\r\n        testName: currentTestName,\r\n        questionNumber: round !== \"2\" ? targetQuestionNumber : currentQuestionNumber,\r\n        round: currentRound,\r\n        packetName: currentRound === \"3\" ? selectedPacketName : undefined\r\n      }\r\n      const question = await gameApi.getQuestions(nextQuestion);\r\n\r\n      return { question, targetQuestionNumber };\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n\r\nexport const submitAnswer = createAsyncThunk(\r\n  'game/submitAnswer',\r\n  async (params: { roomId: string; uid: string; answer: string; time: number }, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Replace with actual API call\r\n      const response = await fetch('/api/game/answer', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(params),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to submit answer');\r\n      }\r\n\r\n      const data = await response.json();\r\n      return data;\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n\r\nexport const updateScores = createAsyncThunk(\r\n  'game/updateScores',\r\n  async (params: { roomId: string; mode: string; scores?: Score[]; round: string }, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Replace with actual API call\r\n      const response = await fetch('/api/game/scoring', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(params),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to update scores');\r\n      }\r\n\r\n      const data = await response.json();\r\n      return data.scores;\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n\r\n// Game slice\r\nconst gameSlice = createSlice({\r\n  name: 'game',\r\n  initialState,\r\n  reducers: {\r\n    // Game state management\r\n    setCurrentRound: (state, action: PayloadAction<string>) => {\r\n      state.currentRound = action.payload;\r\n      state.currentQuestionNumber = 1; // Reset question number when round changes\r\n    },\r\n\r\n    setCurrentTestName: (state, action: PayloadAction<string>) => {\r\n      state.currentTestName = action.payload;\r\n    },\r\n\r\n    setSelectedPacketName: (state, action: PayloadAction<string>) => {\r\n      state.selectedPacketName = action.payload;\r\n    },\r\n\r\n    setIsInputDisabled: (state, action: PayloadAction<boolean>) => {\r\n      state.isInputDisabled = action.payload;\r\n    },\r\n\r\n    setIsActive: (state, action: PayloadAction<boolean>) => {\r\n      state.isActive = action.payload;\r\n    },\r\n\r\n    setIsHost: (state, action: PayloadAction<boolean>) => {\r\n      state.isHost = action.payload;\r\n    },\r\n\r\n    // Question management\r\n    setCurrentQuestion: (state, action: PayloadAction<Question | null>) => {\r\n      state.currentQuestion = action.payload;\r\n    },\r\n\r\n    setQuestions: (state, action: PayloadAction<Question[]>) => {\r\n      state.questions = action.payload;\r\n    },\r\n\r\n    setCurrentCorrectAnswer: (state, action: PayloadAction<string>) => {\r\n      state.currentCorrectAnswer = action.payload;\r\n    },\r\n\r\n    setPlayerAnswerList: (state, action: PayloadAction<Answer[]>) => {\r\n      state.players = state.players.map(player => {\r\n        const answerUpdate = action.payload.find(a => a.uid === player.uid);\r\n        return answerUpdate ? { ...player, ...answerUpdate } : player;\r\n      });\r\n    },\r\n\r\n    clearPlayerAnswerList: (state) => {\r\n      state.players = state.players.map(player => ({\r\n        ...player,\r\n        answer: \"\",\r\n        time: 0,\r\n      }));\r\n    },\r\n\r\n    nextQuestion: (state) => {\r\n      state.currentQuestionNumber += 1;\r\n    },\r\n\r\n    setCurrentQuestionNumber: (state, action: PayloadAction<number>) => {\r\n      state.currentQuestionNumber = action.payload;\r\n    },\r\n    // Player management\r\n    setPlayers: (state, action: PayloadAction<Partial<PlayerData[]>>) => {\r\n      state.players = state.players.map(player => {\r\n        const update = action.payload.find(p => p && p.uid === player.uid);\r\n        return update ? { ...player, ...update } : player;\r\n      });\r\n    },\r\n\r\n    setCurrentPlayer: (state, action: PayloadAction<RoomPlayer>) => {\r\n      state.currentPlayer = action.payload;\r\n    },\r\n\r\n    updatePlayer: (state, action: PayloadAction<{ uid: string; updates: Partial<PlayerData> }>) => {\r\n      const playerIndex = state.players.findIndex(p => p.uid === action.payload.uid);\r\n      if (playerIndex !== -1) {\r\n        state.players[playerIndex] = { ...state.players[playerIndex], ...action.payload.updates };\r\n      }\r\n    },\r\n\r\n    addPlayer: (state, action: PayloadAction<PlayerData>) => {\r\n      const existingIndex = state.players.findIndex(p => p.uid === action.payload.uid);\r\n      if (existingIndex === -1) {\r\n        state.players.push(action.payload);\r\n      }\r\n    },\r\n\r\n    setBuzzedPlayer: (state, action: PayloadAction<string>) => {\r\n      state.buzzedPlayerName = action.payload;\r\n    },\r\n\r\n    removePlayer: (state, action: PayloadAction<string>) => {\r\n      state.players = state.players.filter(p => p.uid !== action.payload);\r\n    },\r\n\r\n    setPlayerAnswer: (state, action: PayloadAction<{ answer: string; time: number }>) => {\r\n      if (state.currentPlayer) {\r\n        state.currentPlayer.answer = action.payload.answer;\r\n        state.currentPlayer.time = action.payload.time;\r\n      }\r\n    },\r\n\r\n    // Scoring\r\n    setScores: (state, action: PayloadAction<Score[]>) => {\r\n      state.scores = action.payload;\r\n    },\r\n\r\n    setScoreRules: (state, action: PayloadAction<ScoreRule>) => {\r\n      state.scoreRules = action.payload;\r\n    },\r\n\r\n    // Round-specific data\r\n    setRound2Grid: (state, action: PayloadAction<Partial<Round2Grid | null>>) => {\r\n      if (action.payload === null) {\r\n        state.round2Grid = null;\r\n      } else {\r\n        state.round2Grid = {\r\n          ...(state.round2Grid || {}),\r\n          ...action.payload,\r\n        };\r\n      }\r\n    },\r\n\r\n    setRound4Grid: (state, action: PayloadAction<Partial<Round4Grid | null>>) => {\r\n      if (action.payload === null) {\r\n        state.round4Grid = null;\r\n      } else {\r\n        state.round4Grid = {\r\n          ...(state.round4Grid || {}),\r\n          ...action.payload,\r\n        };\r\n      }\r\n    },\r\n\r\n    // Game settings\r\n    setMode: (state, action: PayloadAction<'manual' | 'auto' | 'adaptive'>) => {\r\n      state.mode = action.payload;\r\n    },\r\n\r\n    setTimeLimit: (state, action: PayloadAction<number>) => {\r\n      state.timeLimit = action.payload;\r\n    },\r\n\r\n    // UI state\r\n    setShowRules: (state, action: PayloadAction<boolean>) => {\r\n      state.showRules = action.payload;\r\n    },\r\n\r\n    setCurrentTurn: (state, action: PayloadAction<number>) => {\r\n      state.currentTurn = action.payload;\r\n    },\r\n\r\n    setIsBuzzOpen: (state, action: PayloadAction<boolean>) => {\r\n      state.isBuzzOpen = action.payload;\r\n    },\r\n\r\n    // Reset game state\r\n    resetGame: (state) => {\r\n      return { ...initialState, isHost: state.isHost };\r\n    },\r\n\r\n    // Error handling\r\n    clearError: (state) => {\r\n      state.loading.error = null;\r\n    },\r\n  },\r\n\r\n  extraReducers: (builder) => {\r\n    // Join room\r\n    builder\r\n      .addCase(joinRoom.pending, (state) => {\r\n        state.joining.isLoading = true;\r\n        state.joining.error = null;\r\n      })\r\n      .addCase(joinRoom.fulfilled, (state, action) => {\r\n        state.joining.isLoading = false;\r\n        state.players = action.payload.players;\r\n        state.currentPlayer = {\r\n          ...state.currentPlayer,\r\n          ...action.meta.arg,\r\n          uid: action.payload.uid\r\n        }\r\n        state.isHost = false;\r\n      })\r\n      .addCase(joinRoom.rejected, (state, action) => {\r\n        state.joining.isLoading = false;\r\n        state.joining.error = action.payload as string;\r\n      });\r\n    // Fetch questions\r\n    builder\r\n      .addCase(getQuestions.pending, (state) => {\r\n        state.loading.isLoading = true;\r\n        state.loading.error = null;\r\n      })\r\n      .addCase(getQuestions.fulfilled, (state, action) => {\r\n        state.loading.isLoading = false;\r\n        state.currentQuestion = action.payload.question;\r\n        state.currentCorrectAnswer = action.payload.question.answer\r\n        state.currentQuestionNumber = action.payload.targetQuestionNumber\r\n      })\r\n      .addCase(getQuestions.rejected, (state, action) => {\r\n        state.loading.isLoading = false;\r\n        state.loading.error = action.payload as string;\r\n      });\r\n\r\n    // Submit answer\r\n    builder\r\n      .addCase(submitAnswer.pending, (state) => {\r\n        state.loading.isLoading = true;\r\n      })\r\n      .addCase(submitAnswer.fulfilled, (state, action) => {\r\n        state.loading.isLoading = false;\r\n        // Handle answer submission result\r\n      })\r\n      .addCase(submitAnswer.rejected, (state, action) => {\r\n        state.loading.isLoading = false;\r\n        state.loading.error = action.payload as string;\r\n      });\r\n\r\n    // Update scores\r\n    builder\r\n      .addCase(updateScores.fulfilled, (state, action) => {\r\n        state.scores = action.payload;\r\n      })\r\n      .addCase(updateScores.rejected, (state, action) => {\r\n        state.loading.error = action.payload as string;\r\n      });\r\n  },\r\n});\r\n\r\nexport const {\r\n  setCurrentRound,\r\n  setIsActive,\r\n  setIsHost,\r\n  setCurrentQuestion,\r\n  setQuestions,\r\n  setCurrentCorrectAnswer,\r\n  nextQuestion,\r\n  setCurrentQuestionNumber,\r\n  setPlayers,\r\n  setCurrentPlayer,\r\n  setPlayerAnswer,\r\n  setPlayerAnswerList,\r\n  clearPlayerAnswerList,\r\n  updatePlayer,\r\n  addPlayer,\r\n  removePlayer,\r\n  setScores,\r\n  setScoreRules,\r\n  setRound2Grid,\r\n  setBuzzedPlayer,\r\n  setRound4Grid,\r\n  setMode,\r\n  setTimeLimit,\r\n  setShowRules,\r\n  setCurrentTurn,\r\n  resetGame,\r\n  clearError,\r\n  setIsInputDisabled,\r\n  setIsBuzzOpen\r\n} = gameSlice.actions;\r\n\r\nexport default gameSlice.reducer;\r\n"], "mappings": "AAAA;AACA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAE/E,OAAOC,SAAS,MAAM,qCAAqC;AAE3D,OAAOC,OAAO,MAAM,uCAAuC;;AAE3D;AACA,MAAMC,YAAuB,GAAG;EAC9B;EACAC,YAAY,EAAE,GAAG;EACjBC,eAAe,EAAE,EAAE;EACnBC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,KAAK;EAEb;EACAC,eAAe,EAAE,IAAI;EACrBC,kBAAkB,EAAE,EAAE;EACtBC,WAAW,EAAE,EAAE;EACfC,eAAe,EAAE,EAAE;EACnBC,SAAS,EAAE,EAAE;EACbC,oBAAoB,EAAE,EAAE;EACxB;EACAC,OAAO,EAAE,EAAE;EACXC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,EAAE;EACVC,UAAU,EAAE,IAAI;EAEhB;EACAC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,gBAAgB,EAAE,EAAE;EAEpB;EACAC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,EAAE;EAEb;EACAC,SAAS,EAAE,KAAK;EAChBC,WAAW,EAAE,CAAC;EACdC,qBAAqB,EAAE,CAAC;EACxBC,UAAU,EAAE,KAAK;EAEjB;EACAC,OAAO,EAAE;IACPC,SAAS,EAAE,KAAK;IAChBC,KAAK,EAAE;EACT,CAAC;EAED;EACAC,OAAO,EAAE;IACPF,SAAS,EAAE,KAAK;IAChBC,KAAK,EAAE;EACT,CAAC;EAED;EACAE,eAAe,EAAE;AACnB,CAAC;AAGD,OAAO,MAAMC,QAAQ,GAAGhC,gBAAgB,CACtC,eAAe,EACf,OAAOiC,QAAyB,EAAE;EAAEC;AAAgB,CAAC,KAAK;EACxD,IAAI;IACF,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAC,gBAAgB,EAAEC,OAAO,CAACC,GAAG,CAACC,kBAAkB,CAAC;IACrEJ,GAAG,CAACK,YAAY,CAACC,MAAM,CAAC,SAAS,EAAER,QAAQ,CAACS,MAAM,CAAC;IACnD,IAAIT,QAAQ,CAACU,QAAQ,EAAE;MACrBR,GAAG,CAACK,YAAY,CAACC,MAAM,CAAC,UAAU,EAAER,QAAQ,CAACU,QAAQ,CAAC;IACxD;IACA,MAAMC,QAAQ,GAAG,MAAM3C,SAAS,CAAC4C,IAAI,CAACV,GAAG,CAACW,QAAQ,CAAC,CAAC,EAAEb,QAAQ,EAAE;MAAEc,eAAe,EAAE;IAAK,CAAQ,CAAC;IAEjG,OAAOH,QAAQ,CAACI,IAAI;EACtB,CAAC,CAAC,OAAOnB,KAAU,EAAE;IACnB,OAAOK,eAAe,CAACL,KAAK,CAACoB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AACD;AACA,OAAO,MAAMC,YAAY,GAAGlD,gBAAgB,CAC1C,qBAAqB,EACrB,OAAO;EAAEmD,cAAc;EAAEC,MAAM,GAAG,KAAK;EAAEC;AAAqE,CAAC,EAAE;EAAEnB,eAAe;EAAEoB;AAAS,CAAC,KAAK;EACjJ,IAAI;IACF,MAAMC,KAAK,GAAGD,QAAQ,CAAC,CAAwB;IAE/C,MAAM;MAAEjD,eAAe;MAAEoB,qBAAqB;MAAErB,YAAY;MAAEK;IAAmB,CAAC,GAAG8C,KAAK,CAACC,IAAI;IAE/F,MAAMC,oBAAoB,GAAGL,MAAM,IAAID,cAAc,KAAKO,SAAS,GAC/DP,cAAc,GACd1B,qBAAqB,GAAG,CAAC;IAG7B,MAAMkC,YAAY,GAAG;MACnBC,QAAQ,EAAEvD,eAAe;MACzB8C,cAAc,EAAEE,KAAK,KAAK,GAAG,GAAGI,oBAAoB,GAAGhC,qBAAqB;MAC5E4B,KAAK,EAAEjD,YAAY;MACnByD,UAAU,EAAEzD,YAAY,KAAK,GAAG,GAAGK,kBAAkB,GAAGiD;IAC1D,CAAC;IACD,MAAMI,QAAQ,GAAG,MAAM5D,OAAO,CAACgD,YAAY,CAACS,YAAY,CAAC;IAEzD,OAAO;MAAEG,QAAQ;MAAEL;IAAqB,CAAC;EAC3C,CAAC,CAAC,OAAO5B,KAAU,EAAE;IACnB,OAAOK,eAAe,CAACL,KAAK,CAACoB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AAED,OAAO,MAAMc,YAAY,GAAG/D,gBAAgB,CAC1C,mBAAmB,EACnB,OAAOgE,MAAqE,EAAE;EAAE9B;AAAgB,CAAC,KAAK;EACpG,IAAI;IACF;IACA,MAAMU,QAAQ,GAAG,MAAMqB,KAAK,CAAC,kBAAkB,EAAE;MAC/CC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACN,MAAM;IAC7B,CAAC,CAAC;IAEF,IAAI,CAACpB,QAAQ,CAAC2B,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IAEA,MAAMxB,IAAI,GAAG,MAAMJ,QAAQ,CAAC6B,IAAI,CAAC,CAAC;IAClC,OAAOzB,IAAI;EACb,CAAC,CAAC,OAAOnB,KAAU,EAAE;IACnB,OAAOK,eAAe,CAACL,KAAK,CAACoB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AAED,OAAO,MAAMyB,YAAY,GAAG1E,gBAAgB,CAC1C,mBAAmB,EACnB,OAAOgE,MAAyE,EAAE;EAAE9B;AAAgB,CAAC,KAAK;EACxG,IAAI;IACF;IACA,MAAMU,QAAQ,GAAG,MAAMqB,KAAK,CAAC,mBAAmB,EAAE;MAChDC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACN,MAAM;IAC7B,CAAC,CAAC;IAEF,IAAI,CAACpB,QAAQ,CAAC2B,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IAEA,MAAMxB,IAAI,GAAG,MAAMJ,QAAQ,CAAC6B,IAAI,CAAC,CAAC;IAClC,OAAOzB,IAAI,CAAChC,MAAM;EACpB,CAAC,CAAC,OAAOa,KAAU,EAAE;IACnB,OAAOK,eAAe,CAACL,KAAK,CAACoB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;;AAED;AACA,MAAM0B,SAAS,GAAG5E,WAAW,CAAC;EAC5B6E,IAAI,EAAE,MAAM;EACZzE,YAAY;EACZ0E,QAAQ,EAAE;IACR;IACAC,eAAe,EAAEA,CAACvB,KAAK,EAAEwB,MAA6B,KAAK;MACzDxB,KAAK,CAACnD,YAAY,GAAG2E,MAAM,CAACC,OAAO;MACnCzB,KAAK,CAAC9B,qBAAqB,GAAG,CAAC,CAAC,CAAC;IACnC,CAAC;IAEDwD,kBAAkB,EAAEA,CAAC1B,KAAK,EAAEwB,MAA6B,KAAK;MAC5DxB,KAAK,CAAClD,eAAe,GAAG0E,MAAM,CAACC,OAAO;IACxC,CAAC;IAEDE,qBAAqB,EAAEA,CAAC3B,KAAK,EAAEwB,MAA6B,KAAK;MAC/DxB,KAAK,CAAC9C,kBAAkB,GAAGsE,MAAM,CAACC,OAAO;IAC3C,CAAC;IAEDG,kBAAkB,EAAEA,CAAC5B,KAAK,EAAEwB,MAA8B,KAAK;MAC7DxB,KAAK,CAACxB,eAAe,GAAGgD,MAAM,CAACC,OAAO;IACxC,CAAC;IAEDI,WAAW,EAAEA,CAAC7B,KAAK,EAAEwB,MAA8B,KAAK;MACtDxB,KAAK,CAACjD,QAAQ,GAAGyE,MAAM,CAACC,OAAO;IACjC,CAAC;IAEDK,SAAS,EAAEA,CAAC9B,KAAK,EAAEwB,MAA8B,KAAK;MACpDxB,KAAK,CAAChD,MAAM,GAAGwE,MAAM,CAACC,OAAO;IAC/B,CAAC;IAED;IACAM,kBAAkB,EAAEA,CAAC/B,KAAK,EAAEwB,MAAsC,KAAK;MACrExB,KAAK,CAAC/C,eAAe,GAAGuE,MAAM,CAACC,OAAO;IACxC,CAAC;IAEDO,YAAY,EAAEA,CAAChC,KAAK,EAAEwB,MAAiC,KAAK;MAC1DxB,KAAK,CAAC3C,SAAS,GAAGmE,MAAM,CAACC,OAAO;IAClC,CAAC;IAEDQ,uBAAuB,EAAEA,CAACjC,KAAK,EAAEwB,MAA6B,KAAK;MACjExB,KAAK,CAAC1C,oBAAoB,GAAGkE,MAAM,CAACC,OAAO;IAC7C,CAAC;IAEDS,mBAAmB,EAAEA,CAAClC,KAAK,EAAEwB,MAA+B,KAAK;MAC/DxB,KAAK,CAACzC,OAAO,GAAGyC,KAAK,CAACzC,OAAO,CAAC4E,GAAG,CAACC,MAAM,IAAI;QAC1C,MAAMC,YAAY,GAAGb,MAAM,CAACC,OAAO,CAACa,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKJ,MAAM,CAACI,GAAG,CAAC;QACnE,OAAOH,YAAY,GAAG;UAAE,GAAGD,MAAM;UAAE,GAAGC;QAAa,CAAC,GAAGD,MAAM;MAC/D,CAAC,CAAC;IACJ,CAAC;IAEDK,qBAAqB,EAAGzC,KAAK,IAAK;MAChCA,KAAK,CAACzC,OAAO,GAAGyC,KAAK,CAACzC,OAAO,CAAC4E,GAAG,CAACC,MAAM,KAAK;QAC3C,GAAGA,MAAM;QACTM,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL,CAAC;IAEDvC,YAAY,EAAGJ,KAAK,IAAK;MACvBA,KAAK,CAAC9B,qBAAqB,IAAI,CAAC;IAClC,CAAC;IAED0E,wBAAwB,EAAEA,CAAC5C,KAAK,EAAEwB,MAA6B,KAAK;MAClExB,KAAK,CAAC9B,qBAAqB,GAAGsD,MAAM,CAACC,OAAO;IAC9C,CAAC;IACD;IACAoB,UAAU,EAAEA,CAAC7C,KAAK,EAAEwB,MAA4C,KAAK;MACnExB,KAAK,CAACzC,OAAO,GAAGyC,KAAK,CAACzC,OAAO,CAAC4E,GAAG,CAACC,MAAM,IAAI;QAC1C,MAAMU,MAAM,GAAGtB,MAAM,CAACC,OAAO,CAACa,IAAI,CAACS,CAAC,IAAIA,CAAC,IAAIA,CAAC,CAACP,GAAG,KAAKJ,MAAM,CAACI,GAAG,CAAC;QAClE,OAAOM,MAAM,GAAG;UAAE,GAAGV,MAAM;UAAE,GAAGU;QAAO,CAAC,GAAGV,MAAM;MACnD,CAAC,CAAC;IACJ,CAAC;IAEDY,gBAAgB,EAAEA,CAAChD,KAAK,EAAEwB,MAAiC,KAAK;MAC9DxB,KAAK,CAACxC,aAAa,GAAGgE,MAAM,CAACC,OAAO;IACtC,CAAC;IAEDwB,YAAY,EAAEA,CAACjD,KAAK,EAAEwB,MAAoE,KAAK;MAC7F,MAAM0B,WAAW,GAAGlD,KAAK,CAACzC,OAAO,CAAC4F,SAAS,CAACJ,CAAC,IAAIA,CAAC,CAACP,GAAG,KAAKhB,MAAM,CAACC,OAAO,CAACe,GAAG,CAAC;MAC9E,IAAIU,WAAW,KAAK,CAAC,CAAC,EAAE;QACtBlD,KAAK,CAACzC,OAAO,CAAC2F,WAAW,CAAC,GAAG;UAAE,GAAGlD,KAAK,CAACzC,OAAO,CAAC2F,WAAW,CAAC;UAAE,GAAG1B,MAAM,CAACC,OAAO,CAAC2B;QAAQ,CAAC;MAC3F;IACF,CAAC;IAEDC,SAAS,EAAEA,CAACrD,KAAK,EAAEwB,MAAiC,KAAK;MACvD,MAAM8B,aAAa,GAAGtD,KAAK,CAACzC,OAAO,CAAC4F,SAAS,CAACJ,CAAC,IAAIA,CAAC,CAACP,GAAG,KAAKhB,MAAM,CAACC,OAAO,CAACe,GAAG,CAAC;MAChF,IAAIc,aAAa,KAAK,CAAC,CAAC,EAAE;QACxBtD,KAAK,CAACzC,OAAO,CAACgG,IAAI,CAAC/B,MAAM,CAACC,OAAO,CAAC;MACpC;IACF,CAAC;IAED+B,eAAe,EAAEA,CAACxD,KAAK,EAAEwB,MAA6B,KAAK;MACzDxB,KAAK,CAACnC,gBAAgB,GAAG2D,MAAM,CAACC,OAAO;IACzC,CAAC;IAEDgC,YAAY,EAAEA,CAACzD,KAAK,EAAEwB,MAA6B,KAAK;MACtDxB,KAAK,CAACzC,OAAO,GAAGyC,KAAK,CAACzC,OAAO,CAACmG,MAAM,CAACX,CAAC,IAAIA,CAAC,CAACP,GAAG,KAAKhB,MAAM,CAACC,OAAO,CAAC;IACrE,CAAC;IAEDkC,eAAe,EAAEA,CAAC3D,KAAK,EAAEwB,MAAuD,KAAK;MACnF,IAAIxB,KAAK,CAACxC,aAAa,EAAE;QACvBwC,KAAK,CAACxC,aAAa,CAACkF,MAAM,GAAGlB,MAAM,CAACC,OAAO,CAACiB,MAAM;QAClD1C,KAAK,CAACxC,aAAa,CAACmF,IAAI,GAAGnB,MAAM,CAACC,OAAO,CAACkB,IAAI;MAChD;IACF,CAAC;IAED;IACAiB,SAAS,EAAEA,CAAC5D,KAAK,EAAEwB,MAA8B,KAAK;MACpDxB,KAAK,CAACvC,MAAM,GAAG+D,MAAM,CAACC,OAAO;IAC/B,CAAC;IAEDoC,aAAa,EAAEA,CAAC7D,KAAK,EAAEwB,MAAgC,KAAK;MAC1DxB,KAAK,CAACtC,UAAU,GAAG8D,MAAM,CAACC,OAAO;IACnC,CAAC;IAED;IACAqC,aAAa,EAAEA,CAAC9D,KAAK,EAAEwB,MAAiD,KAAK;MAC3E,IAAIA,MAAM,CAACC,OAAO,KAAK,IAAI,EAAE;QAC3BzB,KAAK,CAACrC,UAAU,GAAG,IAAI;MACzB,CAAC,MAAM;QACLqC,KAAK,CAACrC,UAAU,GAAG;UACjB,IAAIqC,KAAK,CAACrC,UAAU,IAAI,CAAC,CAAC,CAAC;UAC3B,GAAG6D,MAAM,CAACC;QACZ,CAAC;MACH;IACF,CAAC;IAEDsC,aAAa,EAAEA,CAAC/D,KAAK,EAAEwB,MAAiD,KAAK;MAC3E,IAAIA,MAAM,CAACC,OAAO,KAAK,IAAI,EAAE;QAC3BzB,KAAK,CAACpC,UAAU,GAAG,IAAI;MACzB,CAAC,MAAM;QACLoC,KAAK,CAACpC,UAAU,GAAG;UACjB,IAAIoC,KAAK,CAACpC,UAAU,IAAI,CAAC,CAAC,CAAC;UAC3B,GAAG4D,MAAM,CAACC;QACZ,CAAC;MACH;IACF,CAAC;IAED;IACAuC,OAAO,EAAEA,CAAChE,KAAK,EAAEwB,MAAqD,KAAK;MACzExB,KAAK,CAAClC,IAAI,GAAG0D,MAAM,CAACC,OAAO;IAC7B,CAAC;IAEDwC,YAAY,EAAEA,CAACjE,KAAK,EAAEwB,MAA6B,KAAK;MACtDxB,KAAK,CAACjC,SAAS,GAAGyD,MAAM,CAACC,OAAO;IAClC,CAAC;IAED;IACAyC,YAAY,EAAEA,CAAClE,KAAK,EAAEwB,MAA8B,KAAK;MACvDxB,KAAK,CAAChC,SAAS,GAAGwD,MAAM,CAACC,OAAO;IAClC,CAAC;IAED0C,cAAc,EAAEA,CAACnE,KAAK,EAAEwB,MAA6B,KAAK;MACxDxB,KAAK,CAAC/B,WAAW,GAAGuD,MAAM,CAACC,OAAO;IACpC,CAAC;IAED2C,aAAa,EAAEA,CAACpE,KAAK,EAAEwB,MAA8B,KAAK;MACxDxB,KAAK,CAAC7B,UAAU,GAAGqD,MAAM,CAACC,OAAO;IACnC,CAAC;IAED;IACA4C,SAAS,EAAGrE,KAAK,IAAK;MACpB,OAAO;QAAE,GAAGpD,YAAY;QAAEI,MAAM,EAAEgD,KAAK,CAAChD;MAAO,CAAC;IAClD,CAAC;IAED;IACAsH,UAAU,EAAGtE,KAAK,IAAK;MACrBA,KAAK,CAAC5B,OAAO,CAACE,KAAK,GAAG,IAAI;IAC5B;EACF,CAAC;EAEDiG,aAAa,EAAGC,OAAO,IAAK;IAC1B;IACAA,OAAO,CACJC,OAAO,CAAChG,QAAQ,CAACiG,OAAO,EAAG1E,KAAK,IAAK;MACpCA,KAAK,CAACzB,OAAO,CAACF,SAAS,GAAG,IAAI;MAC9B2B,KAAK,CAACzB,OAAO,CAACD,KAAK,GAAG,IAAI;IAC5B,CAAC,CAAC,CACDmG,OAAO,CAAChG,QAAQ,CAACkG,SAAS,EAAE,CAAC3E,KAAK,EAAEwB,MAAM,KAAK;MAC9CxB,KAAK,CAACzB,OAAO,CAACF,SAAS,GAAG,KAAK;MAC/B2B,KAAK,CAACzC,OAAO,GAAGiE,MAAM,CAACC,OAAO,CAAClE,OAAO;MACtCyC,KAAK,CAACxC,aAAa,GAAG;QACpB,GAAGwC,KAAK,CAACxC,aAAa;QACtB,GAAGgE,MAAM,CAACoD,IAAI,CAACC,GAAG;QAClBrC,GAAG,EAAEhB,MAAM,CAACC,OAAO,CAACe;MACtB,CAAC;MACDxC,KAAK,CAAChD,MAAM,GAAG,KAAK;IACtB,CAAC,CAAC,CACDyH,OAAO,CAAChG,QAAQ,CAACqG,QAAQ,EAAE,CAAC9E,KAAK,EAAEwB,MAAM,KAAK;MAC7CxB,KAAK,CAACzB,OAAO,CAACF,SAAS,GAAG,KAAK;MAC/B2B,KAAK,CAACzB,OAAO,CAACD,KAAK,GAAGkD,MAAM,CAACC,OAAiB;IAChD,CAAC,CAAC;IACJ;IACA+C,OAAO,CACJC,OAAO,CAAC9E,YAAY,CAAC+E,OAAO,EAAG1E,KAAK,IAAK;MACxCA,KAAK,CAAC5B,OAAO,CAACC,SAAS,GAAG,IAAI;MAC9B2B,KAAK,CAAC5B,OAAO,CAACE,KAAK,GAAG,IAAI;IAC5B,CAAC,CAAC,CACDmG,OAAO,CAAC9E,YAAY,CAACgF,SAAS,EAAE,CAAC3E,KAAK,EAAEwB,MAAM,KAAK;MAClDxB,KAAK,CAAC5B,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/B2B,KAAK,CAAC/C,eAAe,GAAGuE,MAAM,CAACC,OAAO,CAAClB,QAAQ;MAC/CP,KAAK,CAAC1C,oBAAoB,GAAGkE,MAAM,CAACC,OAAO,CAAClB,QAAQ,CAACmC,MAAM;MAC3D1C,KAAK,CAAC9B,qBAAqB,GAAGsD,MAAM,CAACC,OAAO,CAACvB,oBAAoB;IACnE,CAAC,CAAC,CACDuE,OAAO,CAAC9E,YAAY,CAACmF,QAAQ,EAAE,CAAC9E,KAAK,EAAEwB,MAAM,KAAK;MACjDxB,KAAK,CAAC5B,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/B2B,KAAK,CAAC5B,OAAO,CAACE,KAAK,GAAGkD,MAAM,CAACC,OAAiB;IAChD,CAAC,CAAC;;IAEJ;IACA+C,OAAO,CACJC,OAAO,CAACjE,YAAY,CAACkE,OAAO,EAAG1E,KAAK,IAAK;MACxCA,KAAK,CAAC5B,OAAO,CAACC,SAAS,GAAG,IAAI;IAChC,CAAC,CAAC,CACDoG,OAAO,CAACjE,YAAY,CAACmE,SAAS,EAAE,CAAC3E,KAAK,EAAEwB,MAAM,KAAK;MAClDxB,KAAK,CAAC5B,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/B;IACF,CAAC,CAAC,CACDoG,OAAO,CAACjE,YAAY,CAACsE,QAAQ,EAAE,CAAC9E,KAAK,EAAEwB,MAAM,KAAK;MACjDxB,KAAK,CAAC5B,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/B2B,KAAK,CAAC5B,OAAO,CAACE,KAAK,GAAGkD,MAAM,CAACC,OAAiB;IAChD,CAAC,CAAC;;IAEJ;IACA+C,OAAO,CACJC,OAAO,CAACtD,YAAY,CAACwD,SAAS,EAAE,CAAC3E,KAAK,EAAEwB,MAAM,KAAK;MAClDxB,KAAK,CAACvC,MAAM,GAAG+D,MAAM,CAACC,OAAO;IAC/B,CAAC,CAAC,CACDgD,OAAO,CAACtD,YAAY,CAAC2D,QAAQ,EAAE,CAAC9E,KAAK,EAAEwB,MAAM,KAAK;MACjDxB,KAAK,CAAC5B,OAAO,CAACE,KAAK,GAAGkD,MAAM,CAACC,OAAiB;IAChD,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXF,eAAe;EACfM,WAAW;EACXC,SAAS;EACTC,kBAAkB;EAClBC,YAAY;EACZC,uBAAuB;EACvB7B,YAAY;EACZwC,wBAAwB;EACxBC,UAAU;EACVG,gBAAgB;EAChBW,eAAe;EACfzB,mBAAmB;EACnBO,qBAAqB;EACrBQ,YAAY;EACZI,SAAS;EACTI,YAAY;EACZG,SAAS;EACTC,aAAa;EACbC,aAAa;EACbN,eAAe;EACfO,aAAa;EACbC,OAAO;EACPC,YAAY;EACZC,YAAY;EACZC,cAAc;EACdE,SAAS;EACTC,UAAU;EACV1C,kBAAkB;EAClBwC;AACF,CAAC,GAAGhD,SAAS,CAAC2D,OAAO;AAErB,eAAe3D,SAAS,CAAC4D,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}