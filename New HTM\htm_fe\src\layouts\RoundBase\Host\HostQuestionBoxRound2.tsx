import React, { useEffect, useRef, useState } from 'react'
import GameGridRound2 from '../../../components/ui/GameGridRound2'
import { useTimeStart } from '../../../context/timeListenerContext';
import { useSounds } from '../../../context/soundContext';
import { useSearchParams } from 'react-router-dom';
import { Question } from '../../../shared/types';
import { submitAnswer } from '../../services';
import { generateGrid } from '../../../pages/User/Round2/utils';
import { openObstacle, resetBuzz } from '../../../components/services';
import { usePlayer } from '../../../context/playerContext';
import { useGameListenersRound2 } from '../../../hooks/useListenerRound2';
import { useFirebaseListener } from '../../../shared/hooks';
import { useAppDispatch, useAppSelector } from '../../../app/store';


interface MatchPosition {
    x: number;
    y: number;
    dir: number;
}

interface WordObj {
    string: string;
    char: string[];
    totalMatches: number;
    effectiveMatches: number;
    successfulMatches: MatchPosition[];
    x: number;
    y: number;
    dir: number;
    index: number;
};


interface ObstacleQuestionBoxProps {
    obstacleWord?: string;
    hintWordArray?: string[];
    isHost?: boolean;
    initialGrid?: string[][];
    isSpectator?: boolean;
}

const HostQuestionBoxRound2: React.FC<ObstacleQuestionBoxProps> = ({
    obstacleWord,
    hintWordArray,
    initialGrid,
    isSpectator = false,
    isHost = false,
}) => {
    const { startTimer, timeLeft, setTimeLeft, playerAnswerTime } = useTimeStart();
    const sounds = useSounds();
    const [searchParams] = useSearchParams();
    const { setInitialGrid, animationKey, setAnimationKey, playerAnswerRef, position, setAnswerList } = usePlayer();
    const roomId = searchParams.get("roomId") || "";
    const testName = searchParams.get("testName") || ""
    const GRID_SIZE = 30;
    const [grid, setGrid] = useState<string[][]>([[]]);
    const [hintWords, setHintWords] = useState<WordObj[]>([]);
    const [currentQuestion, setCurrentQuestion] = useState<Question>()
    const [buzzedPlayer, setBuzzedPlayer] = useState<string>("");

    const [cellStyles, setCellStyles] = useState<
        Record<string, { background: string; textColor: string }>
    >({}); // Tracks background and text styles
    const [menu, setMenu] = useState<{
        visible: boolean;
        rowIndex?: number;
        colIndex?: number;
    }>({ visible: false });
    const [markedCharacters, setMarkedCharacters] = useState<Record<string, number[]>>({});
    const [highlightedCharacters, setHighlightedCharacters] = useState<Record<string, number[]>>({});
    const [showModal, setShowModal] = useState(false);
    const isInitialTimerMount = useRef(false)
    const menuRef = useRef<HTMLDivElement>(null);

    const {listenToTimeStart, listenToSound, deletePath } = useFirebaseListener(roomId)
    const { revealCells, generateInitialGrid } = useGameListenersRound2({
        roomId,
        grid,
        hintWords,
        isHost,
        hintWordArray,
        obstacleWord,

        setShowModal,
        setCellStyles,
        setMarkedCharacters,
        setHighlightedCharacters,
    })


    useEffect(() => {
        async () => {
            await generateInitialGrid(hintWordArray)
        }
    }, [hintWordArray, obstacleWord, initialGrid])

    useEffect(() => {
            const unsubscribe = listenToTimeStart(
                () => startTimer(15)
            )
            return () => {
                unsubscribe();
            };
    
        }, [])
    
        useEffect(() => {
            const unsubscribeSound = listenToSound(
                () => {
                    deletePath("sound")
                }
            );
    
            return () => {
                unsubscribeSound();
            };
        }, []);


    const handleSuffleGrid = async () => {
        await generateInitialGrid(hintWordArray)
    }

    // Handle number click to show menu
    const handleNumberClick = (rowIndex: number, colIndex: number) => {
        if (!isHost) return;
        setMenu({
            visible: true,
            rowIndex,
            colIndex,
        });
    };

    // Handle menu actions
    const handleMenuAction = (
        action: "open" | "correct" | "incorrect",
        rowIndex: number,
        colIndex: number,
        hintWordNumber: string
    ) => {
        revealCells(rowIndex, colIndex, action, hintWordNumber);
        setMenu({ visible: false });
    };

    const handleCloseModal = () => {
        setShowModal(false);
        // Optionally clear buzzedPlayer if you want to reset it
        setBuzzedPlayer("");
        resetBuzz(roomId)
    };

    const handleOpenObstacle = async () => {
        if (!isHost || !hintWords || !hintWordArray) return;

        // Reveal all horizontal rows immediately
        for (const hintWord of hintWords) {
            revealCells(hintWord.x, hintWord.y, "all", hintWord.index.toString())
        }

        if (obstacleWord) {
            //await openObstacle(roomId, obstacleWord, pla)
        }
    }

    // Close menu on outside click
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                setMenu({ visible: false });
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    return (
        <div className="flex flex-col items-center bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-6 mb-4 relative">
            <div className="text-white text-xl font-semibold text-center mb-4 max-w-[90%]">
                {typeof currentQuestion?.question === "string"
                    ? currentQuestion.question
                    : ""}
            </div>
            <GameGridRound2
                obstacleWord={obstacleWord}
                hintWords={hintWords}

                cellStyles={cellStyles}
                menu={menu}
                menuRef={menuRef}
                isHost={true}
                showModal={showModal}
                isSpectator={isSpectator}

                onNumberClick={handleNumberClick}
                onMenuAction={handleMenuAction}
                onOpenObstacle={handleOpenObstacle}
                onShuffleGrid={handleSuffleGrid}

            />

            {showModal && buzzedPlayer && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <div className="bg-white rounded-lg p-6 w-80 shadow-lg">
                        <h2 className="text-lg font-semibold text-gray-800 mb-4 text-center">
                            {`${buzzedPlayer} đã nhấn chuông trả lời`}
                        </h2>
                        <div className="flex justify-center">
                            <button
                                onClick={handleCloseModal}
                                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400"
                            >
                                Đóng
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}

export default HostQuestionBoxRound2