export const useRound2 = () => {


    const revealCellsForPlayer = (
        rowIndex: number,
        colIndex: number,
        action: "open" | "incorrect" | "correct",
        selectedRowNumber: string,
        markedCharacterIndex: number[],
        isRow?: boolean,
        wordLength?: number, // For open and incorrect
        correctAnswer?: string, // For correct
    ) => {
        if (isHost) return; // Ensure this runs only for players

        // Determine word length
        const length = action === "correct" ? correctAnswer?.length || 0 : wordLength || 0;

        if (length === 0) {
            console.warn(`No valid word length for row ${selectedRowNumber}`);
            return;
        }

        setCellStyles((prev) => {
            const newStyles = { ...prev };
            if (isRow) {
                // Horizontal: style cells from colIndex + 1 to colIndex + length
                for (let col = colIndex + 1; col <= colIndex + length; col++) {
                    const key = `${rowIndex}-${col}`;

                    // Skip empty and number cells
                    if (grid[rowIndex][col] !== "" && !grid[rowIndex][col]?.includes("number")) {
                        if (action === "open") {
                            newStyles[key] = { background: "bg-yellow-200", textColor: "text-transparent" };
                        } else if (action === "incorrect") {
                            newStyles[key] = { background: "bg-gray-400", textColor: "text-transparent" };
                        } else if (action === "correct") {
                            newStyles[key] = {
                                background: "bg-yellow-200",
                                textColor: markedCharacterIndex.includes(col - colIndex - 1) ? "text-red-600" : "text-black"
                            };
                        }
                    }
                }
            } else {
                // Vertical: style cells from rowIndex + 1 to rowIndex + length
                for (let row = rowIndex + 1; row <= rowIndex + length; row++) {
                    const key = `${row}-${colIndex}`;

                    // Skip empty and number cells
                    if (grid[row][colIndex] !== "" && !grid[row][colIndex]?.includes("number")) {
                        if (action === "open") {
                            newStyles[key] = { background: "bg-yellow-200", textColor: "text-transparent" };
                        } else if (action === "incorrect") {
                            newStyles[key] = { background: "bg-gray-400", textColor: "text-transparent" };
                        } else if (action === "correct") {
                            newStyles[key] = {
                                background: "bg-yellow-200",
                                textColor: markedCharacterIndex.includes(row - rowIndex - 1) ? "text-red-600" : "text-black"
                            };
                        }
                    }
                }
            }
            return newStyles;
        });

        // For "correct", update grid to show the actual word
        if (action === "correct" && correctAnswer) {
            setGrid((prevGrid) => {
                const newGrid = prevGrid.map((row) => [...row]);
                if (isRow) {
                    for (let col = colIndex + 1, i = 0; col <= colIndex + length && i < correctAnswer.length; col++, i++) {
                        newGrid[rowIndex][col] = correctAnswer[i];
                    }
                } else {
                    for (let row = rowIndex + 1, i = 0; row <= rowIndex + length && i < correctAnswer.length; row++, i++) {
                        newGrid[row][colIndex] = correctAnswer[i];
                    }
                }
                return newGrid;
            });
        }
    };
}