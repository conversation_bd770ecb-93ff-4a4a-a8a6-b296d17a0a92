import React, { useEffect, useRef, useState } from 'react'
import GameGridRound2 from '../../../components/ui/GameGridRound2'
import { useTimeStart } from '../../../context/timeListenerContext';
import { useSounds } from '../../../context/soundContext';
import { useSearchParams } from 'react-router-dom';
import { Question } from '../../../shared/types';
import { submitAnswer } from '../../services';
import { generateGrid } from '../../../pages/User/Round2/utils';
import { openObstacle, resetBuzz } from '../../../components/services';
import { usePlayer } from '../../../context/playerContext';
import { useGameListenersRound2 } from '../../../hooks/useListenerRound2';
import { useFirebaseListener } from '../../../shared/hooks';


interface MatchPosition {
    x: number;
    y: number;
    dir: number;
}

interface WordObj {
    string: string;
    char: string[];
    totalMatches: number;
    effectiveMatches: number;
    successfulMatches: MatchPosition[];
    x: number;
    y: number;
    dir: number;
    index: number;
};


interface ObstacleQuestionBoxProps {
    obstacleWord?: string;
    hintWordArray?: string[];
    isHost?: boolean;
    initialGrid?: string[][];
    isSpectator?: boolean;
}

const PlayerQuestionBoxRound2: React.FC<ObstacleQuestionBoxProps> = ({
    obstacleWord,
    hintWordArray,
    initialGrid,
    isSpectator = false,
    isHost = false,
}) => {
    const { startTimer, timeLeft, setTimeLeft, playerAnswerTime } = useTimeStart();
    const sounds = useSounds();
    const [searchParams] = useSearchParams();
    const { setInitialGrid, animationKey, setAnimationKey, playerAnswerRef, position, setAnswerList, currentPlayerName, currentPlayerAvatar } = usePlayer();
    const roomId = searchParams.get("roomId") || "";
    const testName = searchParams.get("testName") || ""
    const [grid, setGrid] = useState<string[][]>([[]]);
    const [hintWords, setHintWords] = useState<WordObj[]>([]);
    const [currentQuestion, setCurrentQuestion] = useState<Question>()
    const [buzzedPlayer, setBuzzedPlayer] = useState<string>("");
    const [markedCharacters, setMarkedCharacters] = useState<Record<string, number[]>>({});
    const [highlightedCharacters, setHighlightedCharacters] = useState<Record<string, number[]>>({});
    const [showModal, setShowModal] = useState(false);

    const [cellStyles, setCellStyles] = useState<
        Record<string, { background: string; textColor: string }>
    >({});
    const [menu, setMenu] = useState<{
        visible: boolean;
        rowIndex?: number;
        colIndex?: number;
    }>({ visible: false });

    const isInitialTimerMount = useRef(false)
    const menuRef = useRef<HTMLDivElement>(null);
    const { revealCellsForPlayer } = useGameListenersRound2({
        roomId,
        grid,
        hintWords,
        isHost,
        hintWordArray,
        obstacleWord,

        setShowModal,
        setCellStyles,
        setMarkedCharacters,
        setHighlightedCharacters,
    })

    const { listenToCorrectRow, listenToIncorectRow, listenToSelectRow, listenToObstacle, listenToBuzzedPlayer, listenToTimeStart, listenToSound, deletePath } = useFirebaseListener(roomId)

    useEffect(() => {
        const unsubscribe = listenToTimeStart(
            () => startTimer(15)
        )
        return () => {
            unsubscribe();
        };

    }, [])

    useEffect(() => {
        const unsubscribeSound = listenToSound(
            () => {
                deletePath("sound")
            }
        );

        return () => {
            unsubscribeSound();
        };
    }, []);

    useEffect(() => {
        const unsubscribeCorrectRow = listenToCorrectRow((data) => {
            const audio = sounds['correct_2'];
            if (audio) {
                audio.play();
            }
            revealCellsForPlayer(data.row_index, data.col_index, "correct", data.hint_word_number, data.index_in_target, data.is_row, data.word_length, data.correct_answer)
        })

        return () => {
            unsubscribeCorrectRow();
        };
    }, [])

    useEffect(() => {
        const unsubscribeSelectRow = listenToSelectRow((data) => {
            revealCellsForPlayer(data.row_index, data.col_index, "open", data.hint_word_number, data.index_in_target, data.is_row, data.word_length, data.correct_answer)
        })

        return () => {
            unsubscribeSelectRow();
        };
    }, [])

    useEffect(() => {
        const unsubscribeIncorrectRow = listenToIncorectRow((data) => {
            const audio = sounds['wrong_2'];
            if (audio) {
                audio.play();
            }
            revealCellsForPlayer(data.row_index, data.col_index, "incorrect", data.hint_word_number, data.index_in_target, data.is_row, data.word_length, data.correct_answer)
        })

        return () => {
            unsubscribeIncorrectRow();
        };
    }, [])

    useEffect(() => {
        const unsubscribeBuzzedPlayer = listenToBuzzedPlayer()

        return () => {
            unsubscribeBuzzedPlayer();
        };
    }, [])

    const handleSuffleGrid = async () => {

    }
    const handleNumberClick = () => {

    };

    const handleMenuAction = (
    ) => {

    };


    const handleCloseModal = () => {
        setShowModal(false);
        // Optionally clear buzzedPlayer if you want to reset it
        setBuzzedPlayer("");
        resetBuzz(roomId)
    };

    const handleOpenObstacle = async () => {

    }

    // Close menu on outside click

    return (
        <div className="flex flex-col items-center bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-6 mb-4 relative">
            <div className="text-white text-xl font-semibold text-center mb-4 max-w-[90%]">
                {typeof currentQuestion?.question === "string"
                    ? currentQuestion.question
                    : ""}
            </div>
            <GameGridRound2
                cellStyles={cellStyles}
                hintWords={hintWords}
                obstacleWord={obstacleWord}
                menu={menu}
                menuRef={menuRef}
                isHost={true}
                isSpectator={isSpectator}
                showModal={showModal}



                onNumberClick={handleNumberClick}
                onMenuAction={handleMenuAction}
                onOpenObstacle={handleOpenObstacle}
                onShuffleGrid={handleSuffleGrid}


            />

            {showModal && buzzedPlayer && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <div className="bg-white rounded-lg p-6 w-80 shadow-lg">
                        <h2 className="text-lg font-semibold text-gray-800 mb-4 text-center">
                            {`${buzzedPlayer} đã nhấn chuông trả lời`}
                        </h2>
                        <div className="flex justify-center">
                            <button
                                onClick={handleCloseModal}
                                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400"
                            >
                                Đóng
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}

export default PlayerQuestionBoxRound2