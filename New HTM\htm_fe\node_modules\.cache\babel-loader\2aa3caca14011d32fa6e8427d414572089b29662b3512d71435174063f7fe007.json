{"ast": null, "code": "var _s = $RefreshSig$();\n// Firebase real-time listener hook\nimport { useEffect, useCallback } from 'react';\nimport { useAppDispatch } from '../../../app/store';\nimport { setPlayers, setCurrentQuestion, setRound2Grid, setRound4Grid, setIsInputDisabled, setCurrentCorrectAnswer, setCurrentTurn, setIsBuzzOpen, setBuzzedPlayer } from '../../../app/store/slices/gameSlice';\nimport { setCurrentRoom, setPlayers as setRoomPlayers, setSpectators } from '../../../app/store/slices/roomSlice';\nimport { firebaseRealtimeService } from '../../services/firebase/realtime';\nimport { FirebaseRoomListener } from '../../../services/firebaseServices';\nexport const useFirebaseListener = roomId => {\n  _s();\n  const dispatch = useAppDispatch();\n  const listener = FirebaseRoomListener.getInstance(roomId || '');\n  /**\r\n   * Listen to room data changes\r\n   */\n  const listenToRoom = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToRoom(roomId, data => {\n      if (data) {\n        // Update room state\n        dispatch(setCurrentRoom(data));\n\n        // Call optional callback\n        callback === null || callback === void 0 ? void 0 : callback(data);\n      }\n    });\n  }, [roomId, dispatch]);\n  const listenToTimeStart = useCallback(callback => {\n    if (!roomId) return () => {};\n    dispatch(setIsInputDisabled(false));\n    return listener.listenToTimeStart(callback);\n  }, [roomId, dispatch]);\n  const listenToSound = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToSound(callback);\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to player answers\r\n   */\n  const listenToNewPlayer = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToPlayers(players => {\n      dispatch(setPlayers(players));\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to player answers\r\n   */\n  const listenToBroadcastedAnswer = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToBroadcastedAnswer(players => {\n      dispatch(setPlayers(players));\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to player answers\r\n   */\n  const listenToCurrentTurn = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToCurrentTurn(turn => {\n      dispatch(setCurrentTurn(turn));\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to player answers\r\n   */\n  const listenToOpenBuzz = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToOpenBuzz(isBuzzOpened => {\n      dispatch(setIsBuzzOpen(isBuzzOpened === \"open\"));\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to spectator join\r\n   */\n  const listenToSpectatorJoin = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToSpectatorJoin(() => {\n      dispatch(setSpectators());\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to spectator join\r\n   */\n  const listenToScores = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToScores(scores => {\n      dispatch(setPlayers(scores));\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to player answers\r\n   */\n  const listenToPlayerAnswers = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToPlayerAnswers(roomId, answers => {\n      // Convert to array and update Redux state\n      const playersArray = Object.values(answers);\n      dispatch(setPlayers(playersArray));\n      dispatch(setRoomPlayers(playersArray.map(p => ({\n        ...p,\n        joinedAt: '',\n        isReady: true,\n        isConnected: true,\n        role: 'player'\n      }))));\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(answers);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to current question\r\n   */\n  const listenToCurrentQuestion = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToQuestion(question => {\n      dispatch(setCurrentQuestion(question));\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to correct answer\r\n   */\n  const listenToCorrectAnswer = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToCorrectAnswer(answer => {\n      dispatch(setCurrentCorrectAnswer(answer));\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to selected row\r\n   */\n  const listenToSelectRow = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToSelectRow(data => {\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(data);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to correct row\r\n   */\n  const listenToCorrectRow = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToCorrectRow(data => {\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(data);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to incorrect row\r\n   */\n  const listenToIncorectRow = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToIncorrectRow(data => {\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(data);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to incorrect row\r\n   */\n  const listenToObstacle = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToObstacle(data => {\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(data);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to packets nam\r\n   */\n  const listenToPacketsName = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToPackets(data => {\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(data);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to selected packets name\r\n   */\n  const listenToSelectedPacketName = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToSelectedPacket(data => {\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(data);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to packets nam\r\n   */\n  const listenToUsedPackets = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToUsedTopics(data => {\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(data);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to packets nam\r\n   */\n  const listenToReturnToPacketsSelection = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToUsedTopics(data => {\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(data);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to buzzed player\r\n   */\n  const listenToBuzzedPlayer = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToBuzzing(playerName => {\n      dispatch(setBuzzedPlayer(playerName));\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to game state\r\n   */\n  const listenToGameState = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToGameState(roomId, state => {\n      if (state) {\n        // Update relevant Redux state based on game state\n        if (state.currentRound) {\n          // dispatch(setCurrentRound(state.currentRound));\n        }\n        if (state.isActive !== undefined) {\n          // dispatch(setIsActive(state.isActive));\n        }\n      }\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(state);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to Round 2 grid\r\n   */\n  const listenToRound2Grid = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToRound2Grid(roomId, grid => {\n      if (grid) {\n        dispatch(setRound2Grid(grid));\n      }\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(grid);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to Round 4 grid\r\n   */\n  const listenToRound4Grid = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToRound4Grid(roomId, grid => {\n      if (grid) {\n        dispatch(setRound4Grid(grid));\n      }\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(grid);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Setup all listeners at once\r\n   */\n  const setupAllListeners = useCallback(callbacks => {\n    if (!roomId) return () => {};\n    const unsubscribers = [listenToRoom(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onRoomChange), listenToPlayerAnswers(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onPlayerAnswersChange), listenToCurrentQuestion(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onQuestionChange), listenToScores(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onScoresChange), listenToGameState(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onGameStateChange), listenToRound2Grid(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onRound2GridChange), listenToRound4Grid(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onRound4GridChange)];\n    return () => {\n      unsubscribers.forEach(unsubscribe => unsubscribe());\n    };\n  }, [roomId, listenToRoom, listenToPlayerAnswers, listenToCurrentQuestion, listenToScores, listenToGameState, listenToRound2Grid, listenToRound4Grid]);\n\n  /**\r\n   * Update player data\r\n   */\n  const updatePlayer = useCallback(async (playerId, playerData) => {\n    if (!roomId) return;\n    await firebaseRealtimeService.updatePlayer(roomId, playerId, playerData);\n  }, [roomId]);\n\n  /**\r\n   * Set current question\r\n   */\n  const setCurrentQuestionFirebase = useCallback(async question => {\n    if (!roomId) return;\n    await firebaseRealtimeService.setCurrentQuestion(roomId, question);\n  }, [roomId]);\n\n  /**\r\n   * Update scores\r\n   */\n  const updateScoresFirebase = useCallback(async scores => {\n    if (!roomId) return;\n    await firebaseRealtimeService.updateScores(roomId, scores);\n  }, [roomId]);\n\n  /**\r\n   * Update game state\r\n   */\n  const updateGameStateFirebase = useCallback(async gameState => {\n    if (!roomId) return;\n    await firebaseRealtimeService.updateGameState(roomId, gameState);\n  }, [roomId]);\n\n  /**\r\n   * Setup on disconnect\r\n   */\n  const setupDisconnect = useCallback(async (uid, userData, callback) => {\n    if (!roomId) return;\n    return listener.setupOnDisconnect(roomId, uid, userData, callback);\n  }, [roomId]);\n  const deletePath = async path => {\n    await listener.deletePath(path);\n  };\n\n  /**\r\n   * Cleanup all listeners on unmount\r\n   */\n  useEffect(() => {\n    return () => {\n      firebaseRealtimeService.removeAllListeners();\n    };\n  }, []);\n  return {\n    // Listeners\n    listenToRoom,\n    listenToNewPlayer,\n    listenToSpectatorJoin,\n    listenToPlayerAnswers,\n    listenToCorrectAnswer,\n    listenToBroadcastedAnswer,\n    listenToCurrentQuestion,\n    listenToScores,\n    listenToGameState,\n    listenToRound2Grid,\n    listenToSelectRow,\n    listenToCorrectRow,\n    listenToIncorectRow,\n    listenToObstacle,\n    listenToBuzzedPlayer,\n    listenToRound4Grid,\n    listenToTimeStart,\n    listenToSound,\n    listenToOpenBuzz,\n    listenToCurrentTurn,\n    setupDisconnect,\n    setupAllListeners,\n    // Writers\n    updatePlayer,\n    setCurrentQuestionFirebase,\n    updateScoresFirebase,\n    updateGameStateFirebase,\n    //Delete\n    deletePath\n  };\n};\n_s(useFirebaseListener, \"ShfCGwFAHBFNN8stmxFs8Ar4w5A=\", false, function () {\n  return [useAppDispatch];\n});\nexport default useFirebaseListener;", "map": {"version": 3, "names": ["useEffect", "useCallback", "useAppDispatch", "setPlayers", "setCurrentQuestion", "setRound2Grid", "setRound4Grid", "setIsInputDisabled", "setCurrentCorrectAnswer", "setCurrentTurn", "setIsBuzzOpen", "setBuzzedPlayer", "setCurrentRoom", "setRoomPlayers", "setSpectators", "firebaseRealtimeService", "FirebaseRoomListener", "useFirebaseListener", "roomId", "_s", "dispatch", "listener", "getInstance", "listenToRoom", "callback", "data", "listenToTimeStart", "listenToSound", "listenToNewPlayer", "listenToPlayers", "players", "listenToBroadcastedAnswer", "listenToCurrentTurn", "turn", "listenToOpenBuzz", "isBuzzOpened", "listenToSpectatorJoin", "listenToScores", "scores", "listenToPlayerAnswers", "answers", "players<PERSON><PERSON>y", "Object", "values", "map", "p", "joinedAt", "isReady", "isConnected", "role", "listenToCurrentQuestion", "listenToQuestion", "question", "listenToCorrectAnswer", "answer", "listenToSelectRow", "listenToCorrectRow", "listenToIncorectRow", "listenToIncorrectRow", "listenToObstacle", "listenToPacketsName", "listenToPackets", "listenToSelectedPacketName", "listenToSelectedPacket", "listenToUsedPackets", "listenToUsedTopics", "listenToReturnToPacketsSelection", "listenToBuzzedPlayer", "listenToBuzzing", "<PERSON><PERSON><PERSON>", "listenToGameState", "state", "currentRound", "isActive", "undefined", "listenToRound2Grid", "grid", "listenToRound4Grid", "setupAllListeners", "callbacks", "unsubscribers", "onRoomChange", "onPlayerAnswersChange", "onQuestionChange", "onScoresChange", "onGameStateChange", "onRound2GridChange", "onRound4GridChange", "for<PERSON>ach", "unsubscribe", "updatePlayer", "playerId", "player<PERSON><PERSON>", "setCurrentQuestionFirebase", "updateScoresFirebase", "updateScores", "updateGameStateFirebase", "gameState", "updateGameState", "setupDisconnect", "uid", "userData", "setupOnDisconnect", "deletePath", "path", "removeAllListeners"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/hooks/firebase/useFirebaseListener.ts"], "sourcesContent": ["// Firebase real-time listener hook\r\nimport { useEffect, useCallback } from 'react';\r\nimport { useAppDispatch } from '../../../app/store';\r\nimport {\r\n  setPlayers,\r\n  setCurrentQuestion,\r\n  setScores,\r\n  setRound2Grid,\r\n  setRound4Grid,\r\n  setIsInputDisabled,\r\n  setCurrentCorrectAnswer,\r\n  setCurrentTurn,\r\n  setIsBuzzOpen,\r\n  setBuzzedPlayer\r\n} from '../../../app/store/slices/gameSlice';\r\nimport {\r\n  setCurrentRoom,\r\n  setPlayers as setRoomPlayers,\r\n  setSpectators\r\n} from '../../../app/store/slices/roomSlice';\r\nimport { firebaseRealtimeService } from '../../services/firebase/realtime';\r\nimport { PlayerData, Question, Score, Room } from '../../types';\r\nimport { FirebaseRoomListener } from '../../../services/firebaseServices';\r\n\r\nexport const useFirebaseListener = (roomId: string | null) => {\r\n  const dispatch = useAppDispatch();\r\n  const listener = FirebaseRoomListener.getInstance(roomId || '');\r\n  /**\r\n   * Listen to room data changes\r\n   */\r\n  const listenToRoom = useCallback((callback?: (data: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToRoom(roomId, (data) => {\r\n      if (data) {\r\n        // Update room state\r\n        dispatch(setCurrentRoom(data as Room));\r\n\r\n        // Call optional callback\r\n        callback?.(data);\r\n      }\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  const listenToTimeStart = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n    dispatch(setIsInputDisabled(false))\r\n\r\n    return listener.listenToTimeStart(\r\n      callback\r\n    );\r\n  }, [roomId, dispatch]);\r\n\r\n  const listenToSound = useCallback((callback: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToSound(\r\n      callback\r\n    );\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to player answers\r\n   */\r\n  const listenToNewPlayer = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToPlayers((players)=> {\r\n      dispatch(setPlayers(players))\r\n\r\n      callback?.()\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to player answers\r\n   */\r\n  const listenToBroadcastedAnswer = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToBroadcastedAnswer((players)=> {\r\n      dispatch(setPlayers(players))\r\n\r\n      callback?.()\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to player answers\r\n   */\r\n  const listenToCurrentTurn = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToCurrentTurn((turn)=> {\r\n      dispatch(setCurrentTurn(turn))\r\n\r\n      callback?.()\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to player answers\r\n   */\r\n  const listenToOpenBuzz = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToOpenBuzz((isBuzzOpened)=> {\r\n      dispatch(setIsBuzzOpen(isBuzzOpened === \"open\"))\r\n\r\n      callback?.()\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to spectator join\r\n   */\r\n  const listenToSpectatorJoin = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToSpectatorJoin(()=> {\r\n      dispatch(setSpectators())\r\n\r\n      callback?.()\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to spectator join\r\n   */\r\n  const listenToScores = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToScores((scores)=> {\r\n      dispatch(setPlayers(scores))\r\n\r\n      callback?.()\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to player answers\r\n   */\r\n  const listenToPlayerAnswers = useCallback((callback?: (answers: Record<string, PlayerData>) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToPlayerAnswers(roomId, (answers) => {\r\n      // Convert to array and update Redux state\r\n      const playersArray = Object.values(answers);\r\n      dispatch(setPlayers(playersArray));\r\n      dispatch(setRoomPlayers(playersArray.map(p => ({ ...p, joinedAt: '', isReady: true, isConnected: true, role: 'player' as const }))));\r\n\r\n      // Call optional callback\r\n      callback?.(answers);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to current question\r\n   */\r\n  const listenToCurrentQuestion = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToQuestion((question) => {\r\n      dispatch(setCurrentQuestion(question));\r\n\r\n      // Call optional callback\r\n      callback?.();\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to correct answer\r\n   */\r\n  const listenToCorrectAnswer = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToCorrectAnswer((answer) => {\r\n      dispatch(setCurrentCorrectAnswer(answer));\r\n\r\n      // Call optional callback\r\n      callback?.();\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to selected row\r\n   */\r\n  const listenToSelectRow = useCallback((callback?: (data: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToSelectRow((data) => {\r\n\r\n      // Call optional callback\r\n      callback?.(data);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to correct row\r\n   */\r\n  const listenToCorrectRow = useCallback((callback?: (data: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToCorrectRow((data) => {\r\n\r\n      // Call optional callback\r\n      callback?.(data);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to incorrect row\r\n   */\r\n  const listenToIncorectRow = useCallback((callback?: (data: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToIncorrectRow((data) => {\r\n\r\n      // Call optional callback\r\n      callback?.(data);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to incorrect row\r\n   */\r\n  const listenToObstacle = useCallback((callback?: (data: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToObstacle((data) => {\r\n\r\n      // Call optional callback\r\n      callback?.(data);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to packets nam\r\n   */\r\n  const listenToPacketsName = useCallback((callback?: (data: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToPackets((data) => {\r\n\r\n      // Call optional callback\r\n      callback?.(data);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to selected packets name\r\n   */\r\n  const listenToSelectedPacketName = useCallback((callback?: (data: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToSelectedPacket((data) => {\r\n\r\n      // Call optional callback\r\n      callback?.(data);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to packets nam\r\n   */\r\n  const listenToUsedPackets = useCallback((callback?: (data: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToUsedTopics((data) => {\r\n\r\n      // Call optional callback\r\n      callback?.(data);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to packets nam\r\n   */\r\n  const listenToReturnToPacketsSelection = useCallback((callback?: (data: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToUsedTopics((data) => {\r\n\r\n      // Call optional callback\r\n      callback?.(data);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to buzzed player\r\n   */\r\n  const listenToBuzzedPlayer = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToBuzzing((playerName) => {\r\n      dispatch(setBuzzedPlayer(playerName));\r\n      // Call optional callback\r\n      callback?.();\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n\r\n\r\n\r\n\r\n  /**\r\n   * Listen to game state\r\n   */\r\n  const listenToGameState = useCallback((callback?: (state: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToGameState(roomId, (state) => {\r\n      if (state) {\r\n        // Update relevant Redux state based on game state\r\n        if (state.currentRound) {\r\n          // dispatch(setCurrentRound(state.currentRound));\r\n        }\r\n        if (state.isActive !== undefined) {\r\n          // dispatch(setIsActive(state.isActive));\r\n        }\r\n      }\r\n\r\n      // Call optional callback\r\n      callback?.(state);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to Round 2 grid\r\n   */\r\n  const listenToRound2Grid = useCallback((callback?: (grid: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToRound2Grid(roomId, (grid) => {\r\n      if (grid) {\r\n        dispatch(setRound2Grid(grid));\r\n      }\r\n\r\n      // Call optional callback\r\n      callback?.(grid);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to Round 4 grid\r\n   */\r\n  const listenToRound4Grid = useCallback((callback?: (grid: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToRound4Grid(roomId, (grid) => {\r\n      if (grid) {\r\n        dispatch(setRound4Grid(grid));\r\n      }\r\n\r\n      // Call optional callback\r\n      callback?.(grid);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Setup all listeners at once\r\n   */\r\n  const setupAllListeners = useCallback((callbacks?: {\r\n    onRoomChange?: (data: any) => void;\r\n    onPlayerAnswersChange?: (answers: Record<string, PlayerData>) => void;\r\n    onQuestionChange?: () => void;\r\n    onScoresChange?: () => void;\r\n    onGameStateChange?: (state: any) => void;\r\n    onRound2GridChange?: (grid: any) => void;\r\n    onRound4GridChange?: (grid: any) => void;\r\n  }) => {\r\n    if (!roomId) return () => { };\r\n\r\n    const unsubscribers = [\r\n      listenToRoom(callbacks?.onRoomChange),\r\n      listenToPlayerAnswers(callbacks?.onPlayerAnswersChange),\r\n      listenToCurrentQuestion(callbacks?.onQuestionChange),\r\n      listenToScores(callbacks?.onScoresChange),\r\n      listenToGameState(callbacks?.onGameStateChange),\r\n      listenToRound2Grid(callbacks?.onRound2GridChange),\r\n      listenToRound4Grid(callbacks?.onRound4GridChange),\r\n    ];\r\n\r\n    return () => {\r\n      unsubscribers.forEach(unsubscribe => unsubscribe());\r\n    };\r\n  }, [\r\n    roomId,\r\n    listenToRoom,\r\n    listenToPlayerAnswers,\r\n    listenToCurrentQuestion,\r\n    listenToScores,\r\n    listenToGameState,\r\n    listenToRound2Grid,\r\n    listenToRound4Grid,\r\n  ]);\r\n\r\n  /**\r\n   * Update player data\r\n   */\r\n  const updatePlayer = useCallback(async (playerId: string, playerData: Partial<PlayerData>) => {\r\n    if (!roomId) return;\r\n\r\n    await firebaseRealtimeService.updatePlayer(roomId, playerId, playerData);\r\n  }, [roomId]);\r\n\r\n  /**\r\n   * Set current question\r\n   */\r\n  const setCurrentQuestionFirebase = useCallback(async (question: Question) => {\r\n    if (!roomId) return;\r\n\r\n    await firebaseRealtimeService.setCurrentQuestion(roomId, question);\r\n  }, [roomId]);\r\n\r\n  /**\r\n   * Update scores\r\n   */\r\n  const updateScoresFirebase = useCallback(async (scores: Score[]) => {\r\n    if (!roomId) return;\r\n\r\n    await firebaseRealtimeService.updateScores(roomId, scores);\r\n  }, [roomId]);\r\n\r\n  /**\r\n   * Update game state\r\n   */\r\n  const updateGameStateFirebase = useCallback(async (gameState: any) => {\r\n    if (!roomId) return;\r\n\r\n    await firebaseRealtimeService.updateGameState(roomId, gameState);\r\n  }, [roomId]);\r\n\r\n  /**\r\n   * Setup on disconnect\r\n   */\r\n  const setupDisconnect = useCallback(async (uid: string, userData: any, callback?: () => void) => {\r\n    if (!roomId) return;\r\n\r\n    return listener.setupOnDisconnect(roomId, uid, userData, callback);\r\n  }, [roomId]);\r\n\r\n  const deletePath = async (path: string): Promise<void> => {\r\n    await listener.deletePath(path);\r\n  }\r\n\r\n  /**\r\n   * Cleanup all listeners on unmount\r\n   */\r\n  useEffect(() => {\r\n    return () => {\r\n      firebaseRealtimeService.removeAllListeners();\r\n    };\r\n  }, []);\r\n\r\n  return {\r\n    // Listeners\r\n    listenToRoom,\r\n    listenToNewPlayer,\r\n    listenToSpectatorJoin,\r\n    listenToPlayerAnswers,\r\n    listenToCorrectAnswer,\r\n    listenToBroadcastedAnswer,\r\n    listenToCurrentQuestion,\r\n    listenToScores,\r\n    listenToGameState,\r\n\r\n    listenToRound2Grid,\r\n    listenToSelectRow,\r\n    listenToCorrectRow,\r\n    listenToIncorectRow,\r\n    listenToObstacle,\r\n    listenToBuzzedPlayer,\r\n\r\n    listenToRound4Grid,\r\n    listenToTimeStart,\r\n    listenToSound,\r\n    listenToOpenBuzz,\r\n    listenToCurrentTurn,\r\n\r\n    setupDisconnect,\r\n    setupAllListeners,\r\n\r\n    // Writers\r\n    updatePlayer,\r\n    setCurrentQuestionFirebase,\r\n    updateScoresFirebase,\r\n    updateGameStateFirebase,\r\n\r\n    //Delete\r\n    deletePath\r\n  };\r\n};\r\n\r\nexport default useFirebaseListener;\r\n"], "mappings": ";AAAA;AACA,SAASA,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC9C,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SACEC,UAAU,EACVC,kBAAkB,EAElBC,aAAa,EACbC,aAAa,EACbC,kBAAkB,EAClBC,uBAAuB,EACvBC,cAAc,EACdC,aAAa,EACbC,eAAe,QACV,qCAAqC;AAC5C,SACEC,cAAc,EACdT,UAAU,IAAIU,cAAc,EAC5BC,aAAa,QACR,qCAAqC;AAC5C,SAASC,uBAAuB,QAAQ,kCAAkC;AAE1E,SAASC,oBAAoB,QAAQ,oCAAoC;AAEzE,OAAO,MAAMC,mBAAmB,GAAIC,MAAqB,IAAK;EAAAC,EAAA;EAC5D,MAAMC,QAAQ,GAAGlB,cAAc,CAAC,CAAC;EACjC,MAAMmB,QAAQ,GAAGL,oBAAoB,CAACM,WAAW,CAACJ,MAAM,IAAI,EAAE,CAAC;EAC/D;AACF;AACA;EACE,MAAMK,YAAY,GAAGtB,WAAW,CAAEuB,QAA8B,IAAK;IACnE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAACQ,YAAY,CAACL,MAAM,EAAGO,IAAI,IAAK;MAC5D,IAAIA,IAAI,EAAE;QACR;QACAL,QAAQ,CAACR,cAAc,CAACa,IAAY,CAAC,CAAC;;QAEtC;QACAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGC,IAAI,CAAC;MAClB;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,MAAM,EAAEE,QAAQ,CAAC,CAAC;EAEtB,MAAMM,iBAAiB,GAAGzB,WAAW,CAAEuB,QAAqB,IAAK;IAC/D,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAC7BE,QAAQ,CAACb,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAEnC,OAAOc,QAAQ,CAACK,iBAAiB,CAC/BF,QACF,CAAC;EACH,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;EAEtB,MAAMO,aAAa,GAAG1B,WAAW,CAAEuB,QAAoB,IAAK;IAC1D,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACM,aAAa,CAC3BH,QACF,CAAC;EACH,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMQ,iBAAiB,GAAG3B,WAAW,CAAEuB,QAAqB,IAAK;IAC/D,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACQ,eAAe,CAAEC,OAAO,IAAI;MAC1CV,QAAQ,CAACjB,UAAU,CAAC2B,OAAO,CAAC,CAAC;MAE7BN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMW,yBAAyB,GAAG9B,WAAW,CAAEuB,QAAqB,IAAK;IACvE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACU,yBAAyB,CAAED,OAAO,IAAI;MACpDV,QAAQ,CAACjB,UAAU,CAAC2B,OAAO,CAAC,CAAC;MAE7BN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMY,mBAAmB,GAAG/B,WAAW,CAAEuB,QAAqB,IAAK;IACjE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACW,mBAAmB,CAAEC,IAAI,IAAI;MAC3Cb,QAAQ,CAACX,cAAc,CAACwB,IAAI,CAAC,CAAC;MAE9BT,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMc,gBAAgB,GAAGjC,WAAW,CAAEuB,QAAqB,IAAK;IAC9D,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACa,gBAAgB,CAAEC,YAAY,IAAI;MAChDf,QAAQ,CAACV,aAAa,CAACyB,YAAY,KAAK,MAAM,CAAC,CAAC;MAEhDX,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMgB,qBAAqB,GAAGnC,WAAW,CAAEuB,QAAqB,IAAK;IACnE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACe,qBAAqB,CAAC,MAAK;MACzChB,QAAQ,CAACN,aAAa,CAAC,CAAC,CAAC;MAEzBU,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMiB,cAAc,GAAGpC,WAAW,CAAEuB,QAAqB,IAAK;IAC5D,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACgB,cAAc,CAAEC,MAAM,IAAI;MACxClB,QAAQ,CAACjB,UAAU,CAACmC,MAAM,CAAC,CAAC;MAE5Bd,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMmB,qBAAqB,GAAGtC,WAAW,CAAEuB,QAAwD,IAAK;IACtG,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAACwB,qBAAqB,CAACrB,MAAM,EAAGsB,OAAO,IAAK;MACxE;MACA,MAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAACH,OAAO,CAAC;MAC3CpB,QAAQ,CAACjB,UAAU,CAACsC,YAAY,CAAC,CAAC;MAClCrB,QAAQ,CAACP,cAAc,CAAC4B,YAAY,CAACG,GAAG,CAACC,CAAC,KAAK;QAAE,GAAGA,CAAC;QAAEC,QAAQ,EAAE,EAAE;QAAEC,OAAO,EAAE,IAAI;QAAEC,WAAW,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEpI;MACAzB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGgB,OAAO,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACtB,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAM8B,uBAAuB,GAAGjD,WAAW,CAAEuB,QAAqB,IAAK;IACrE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAAC8B,gBAAgB,CAAEC,QAAQ,IAAK;MAC7ChC,QAAQ,CAAChB,kBAAkB,CAACgD,QAAQ,CAAC,CAAC;;MAEtC;MACA5B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMiC,qBAAqB,GAAGpD,WAAW,CAAEuB,QAAqB,IAAK;IACnE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACgC,qBAAqB,CAAEC,MAAM,IAAK;MAChDlC,QAAQ,CAACZ,uBAAuB,CAAC8C,MAAM,CAAC,CAAC;;MAEzC;MACA9B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMmC,iBAAiB,GAAGtD,WAAW,CAAEuB,QAA8B,IAAK;IACxE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACkC,iBAAiB,CAAE9B,IAAI,IAAK;MAE1C;MACAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGC,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMoC,kBAAkB,GAAGvD,WAAW,CAAEuB,QAA8B,IAAK;IACzE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACmC,kBAAkB,CAAE/B,IAAI,IAAK;MAE3C;MACAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGC,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMqC,mBAAmB,GAAGxD,WAAW,CAAEuB,QAA8B,IAAK;IAC1E,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACqC,oBAAoB,CAAEjC,IAAI,IAAK;MAE7C;MACAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGC,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMuC,gBAAgB,GAAG1D,WAAW,CAAEuB,QAA8B,IAAK;IACvE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACsC,gBAAgB,CAAElC,IAAI,IAAK;MAEzC;MACAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGC,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMwC,mBAAmB,GAAG3D,WAAW,CAAEuB,QAA8B,IAAK;IAC1E,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACwC,eAAe,CAAEpC,IAAI,IAAK;MAExC;MACAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGC,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAM0C,0BAA0B,GAAG7D,WAAW,CAAEuB,QAA8B,IAAK;IACjF,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAAC0C,sBAAsB,CAAEtC,IAAI,IAAK;MAE/C;MACAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGC,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAM4C,mBAAmB,GAAG/D,WAAW,CAAEuB,QAA8B,IAAK;IAC1E,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAAC4C,kBAAkB,CAAExC,IAAI,IAAK;MAE3C;MACAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGC,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAM8C,gCAAgC,GAAGjE,WAAW,CAAEuB,QAA8B,IAAK;IACvF,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAAC4C,kBAAkB,CAAExC,IAAI,IAAK;MAE3C;MACAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGC,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAM+C,oBAAoB,GAAGlE,WAAW,CAAEuB,QAAqB,IAAK;IAClE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAAC+C,eAAe,CAAEC,UAAU,IAAK;MAC9CjD,QAAQ,CAACT,eAAe,CAAC0D,UAAU,CAAC,CAAC;MACrC;MACA7C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAMtB;AACF;AACA;EACE,MAAMkD,iBAAiB,GAAGrE,WAAW,CAAEuB,QAA+B,IAAK;IACzE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAACuD,iBAAiB,CAACpD,MAAM,EAAGqD,KAAK,IAAK;MAClE,IAAIA,KAAK,EAAE;QACT;QACA,IAAIA,KAAK,CAACC,YAAY,EAAE;UACtB;QAAA;QAEF,IAAID,KAAK,CAACE,QAAQ,KAAKC,SAAS,EAAE;UAChC;QAAA;MAEJ;;MAEA;MACAlD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG+C,KAAK,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACrD,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMuD,kBAAkB,GAAG1E,WAAW,CAAEuB,QAA8B,IAAK;IACzE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAAC4D,kBAAkB,CAACzD,MAAM,EAAG0D,IAAI,IAAK;MAClE,IAAIA,IAAI,EAAE;QACRxD,QAAQ,CAACf,aAAa,CAACuE,IAAI,CAAC,CAAC;MAC/B;;MAEA;MACApD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGoD,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1D,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMyD,kBAAkB,GAAG5E,WAAW,CAAEuB,QAA8B,IAAK;IACzE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAAC8D,kBAAkB,CAAC3D,MAAM,EAAG0D,IAAI,IAAK;MAClE,IAAIA,IAAI,EAAE;QACRxD,QAAQ,CAACd,aAAa,CAACsE,IAAI,CAAC,CAAC;MAC/B;;MAEA;MACApD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGoD,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1D,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAM0D,iBAAiB,GAAG7E,WAAW,CAAE8E,SAQtC,IAAK;IACJ,IAAI,CAAC7D,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,MAAM8D,aAAa,GAAG,CACpBzD,YAAY,CAACwD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,YAAY,CAAC,EACrC1C,qBAAqB,CAACwC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,qBAAqB,CAAC,EACvDhC,uBAAuB,CAAC6B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEI,gBAAgB,CAAC,EACpD9C,cAAc,CAAC0C,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEK,cAAc,CAAC,EACzCd,iBAAiB,CAACS,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEM,iBAAiB,CAAC,EAC/CV,kBAAkB,CAACI,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEO,kBAAkB,CAAC,EACjDT,kBAAkB,CAACE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEQ,kBAAkB,CAAC,CAClD;IAED,OAAO,MAAM;MACXP,aAAa,CAACQ,OAAO,CAACC,WAAW,IAAIA,WAAW,CAAC,CAAC,CAAC;IACrD,CAAC;EACH,CAAC,EAAE,CACDvE,MAAM,EACNK,YAAY,EACZgB,qBAAqB,EACrBW,uBAAuB,EACvBb,cAAc,EACdiC,iBAAiB,EACjBK,kBAAkB,EAClBE,kBAAkB,CACnB,CAAC;;EAEF;AACF;AACA;EACE,MAAMa,YAAY,GAAGzF,WAAW,CAAC,OAAO0F,QAAgB,EAAEC,UAA+B,KAAK;IAC5F,IAAI,CAAC1E,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAAC2E,YAAY,CAACxE,MAAM,EAAEyE,QAAQ,EAAEC,UAAU,CAAC;EAC1E,CAAC,EAAE,CAAC1E,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAM2E,0BAA0B,GAAG5F,WAAW,CAAC,MAAOmD,QAAkB,IAAK;IAC3E,IAAI,CAAClC,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAACX,kBAAkB,CAACc,MAAM,EAAEkC,QAAQ,CAAC;EACpE,CAAC,EAAE,CAAClC,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAM4E,oBAAoB,GAAG7F,WAAW,CAAC,MAAOqC,MAAe,IAAK;IAClE,IAAI,CAACpB,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAACgF,YAAY,CAAC7E,MAAM,EAAEoB,MAAM,CAAC;EAC5D,CAAC,EAAE,CAACpB,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAM8E,uBAAuB,GAAG/F,WAAW,CAAC,MAAOgG,SAAc,IAAK;IACpE,IAAI,CAAC/E,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAACmF,eAAe,CAAChF,MAAM,EAAE+E,SAAS,CAAC;EAClE,CAAC,EAAE,CAAC/E,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAMiF,eAAe,GAAGlG,WAAW,CAAC,OAAOmG,GAAW,EAAEC,QAAa,EAAE7E,QAAqB,KAAK;IAC/F,IAAI,CAACN,MAAM,EAAE;IAEb,OAAOG,QAAQ,CAACiF,iBAAiB,CAACpF,MAAM,EAAEkF,GAAG,EAAEC,QAAQ,EAAE7E,QAAQ,CAAC;EACpE,CAAC,EAAE,CAACN,MAAM,CAAC,CAAC;EAEZ,MAAMqF,UAAU,GAAG,MAAOC,IAAY,IAAoB;IACxD,MAAMnF,QAAQ,CAACkF,UAAU,CAACC,IAAI,CAAC;EACjC,CAAC;;EAED;AACF;AACA;EACExG,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXe,uBAAuB,CAAC0F,kBAAkB,CAAC,CAAC;IAC9C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACL;IACAlF,YAAY;IACZK,iBAAiB;IACjBQ,qBAAqB;IACrBG,qBAAqB;IACrBc,qBAAqB;IACrBtB,yBAAyB;IACzBmB,uBAAuB;IACvBb,cAAc;IACdiC,iBAAiB;IAEjBK,kBAAkB;IAClBpB,iBAAiB;IACjBC,kBAAkB;IAClBC,mBAAmB;IACnBE,gBAAgB;IAChBQ,oBAAoB;IAEpBU,kBAAkB;IAClBnD,iBAAiB;IACjBC,aAAa;IACbO,gBAAgB;IAChBF,mBAAmB;IAEnBmE,eAAe;IACfrB,iBAAiB;IAEjB;IACAY,YAAY;IACZG,0BAA0B;IAC1BC,oBAAoB;IACpBE,uBAAuB;IAEvB;IACAO;EACF,CAAC;AACH,CAAC;AAACpF,EAAA,CApdWF,mBAAmB;EAAA,QACbf,cAAc;AAAA;AAqdjC,eAAee,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}